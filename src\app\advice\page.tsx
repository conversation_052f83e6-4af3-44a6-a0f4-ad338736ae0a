"use client";

const ADVICE_TOPICS = [
  { icon: "https://ext.same-assets.com/4123950039/3695065807.svg", label: "Choosing interior colors" },
  { icon: "https://ext.same-assets.com/4123950039/640793450.svg", label: "Furniture layout tips" },
  { icon: "https://ext.same-assets.com/4123950039/488564308.svg", label: "Lighting for ambiance" },
  { icon: "https://ext.same-assets.com/4123950039/4211619074.svg", label: "Space-saving design" },
  { icon: "https://ext.same-assets.com/4123950039/806734161.svg", label: "Modern vs classic" }
];

export default function AdvicePage() {
  return (
    <main className="max-w-4xl mx-auto px-4 py-16">
      <h1 className="text-3xl md:text-5xl font-bold text-white mb-6">Interior Design Advice</h1>
      <div className="bg-teal-900/70 border-2 border-teal-700 rounded-xl p-6 flex flex-col items-center mb-10">
        <p className="text-lg text-center text-white">Get actionable advice on styling, layout, decor, and more.</p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        {ADVICE_TOPICS.map(topic => (
          <div key={topic.label} className="flex items-center gap-4 bg-gray-900 rounded-xl border-2 border-gray-800 p-5">
            <img src={topic.icon} alt="" className="w-12 h-12 rounded bg-gray-950 p-2" />
            <div className="text-lg font-semibold text-white">{topic.label}</div>
          </div>
        ))}
      </div>
    </main>
  );
}
