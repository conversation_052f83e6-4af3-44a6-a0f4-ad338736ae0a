"use client";
export default function BillingPage() {
  return (
    <main className="max-w-4xl mx-auto px-4 py-16">
      <div className="flex flex-col md:flex-row gap-10 items-center md:items-stretch">
        <div className="bg-gray-900 border-2 border-gray-800 rounded-xl shadow-lg flex-1 flex flex-col items-start p-8 gap-6">
          <div className="text-2xl font-bold text-white mb-2">Billing & Invoices</div>
          <div className="text-gray-300 text-base mb-4">Altora partners with <img src="https://ext.same-assets.com/4123950039/2859947330.svg" alt="Stripe" className="inline-block h-6 ml-2 align-middle" /> for simplified billing.</div>
          <a href="https://stripe.com/billing" target="_blank" rel="noopener noreferrer" className="inline-block text-xs text-teal-400 hover:underline mt-2">Learn more about Stripe Billing</a>
        </div>
        <div className="bg-gray-950 border-2 border-gray-800 rounded-xl shadow-lg flex-1 flex flex-col items-start p-8 gap-6 w-full max-w-md">
          <div className="text-xl text-gray-100 font-semibold mb-4">Log in to manage your account</div>
          <div className="text-gray-400 mb-3">Enter your email and we will send you a link to your customer portal.</div>
          <form>
            <input type="email" placeholder="Email" className="w-full p-3 rounded-lg border border-gray-700 bg-white text-gray-700 font-medium focus:outline-none focus:ring-2 focus:ring-teal-500 mb-3" />
            <button type="submit" className="w-full mt-2 px-5 py-3 rounded-lg bg-teal-600 text-white font-semibold text-lg border border-teal-400 hover:bg-teal-500 hover:border-teal-300 transition">Send</button>
          </form>
        </div>
      </div>
    </main>
  );
}
