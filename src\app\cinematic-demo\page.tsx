"use client";
import React, { useState } from 'react';
import { StyleCardSelector } from '../../components/StyleCardSelector';

export default function CinematicDemoPage() {
  const [selectedStyles, setSelectedStyles] = useState<number[]>([]);

  const handleStyleToggle = (styleId: number) => {
    setSelectedStyles(prev => 
      prev.includes(styleId)
        ? prev.filter(id => id !== styleId)
        : [...prev, styleId]
    );
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🎬 Cinematic Production Design Studio
          </h1>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto">
            Professional design tools specifically crafted for TV series, movies, and entertainment productions. 
            Create authentic sets, period pieces, and cinematic environments with our comprehensive design system 
            featuring 110+ styles across 9 categories including dedicated cinematic genres.
          </p>
          <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="bg-gray-800/50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-teal-400">🎭</div>
              <div className="text-white font-medium">Set Design</div>
              <div className="text-gray-400">Professional sets</div>
            </div>
            <div className="bg-gray-800/50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-teal-400">🎥</div>
              <div className="text-white font-medium">Film Production</div>
              <div className="text-gray-400">Movie aesthetics</div>
            </div>
            <div className="bg-gray-800/50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-teal-400">📺</div>
              <div className="text-white font-medium">TV Series</div>
              <div className="text-gray-400">Episode consistency</div>
            </div>
            <div className="bg-gray-800/50 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-teal-400">🏛️</div>
              <div className="text-white font-medium">Period Pieces</div>
              <div className="text-gray-400">Historical accuracy</div>
            </div>
          </div>
        </div>

        {/* Industry Features */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">🎪 Entertainment Industry Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-3">🎬 Period Accuracy</h3>
              <p className="text-gray-400 text-sm">
                Authentic period styles from Victorian era to 1980s neon. Each style includes historical context 
                and accurate design elements for period dramas and historical productions.
              </p>
            </div>
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-3">🌍 Cultural Authenticity</h3>
              <p className="text-gray-400 text-sm">
                Diverse cultural styles from around the world including Asian, European, Latin American, 
                and regional variations for authentic international productions.
              </p>
            </div>
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-3">🎭 Genre-Specific</h3>
              <p className="text-gray-400 text-sm">
                Specialized styles for different genres: Film Noir, Sci-Fi Laboratory, Horror Gothic, 
                Romantic Comedy, and more for genre-appropriate set design.
              </p>
            </div>
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-3">🗿 Sculpture & Props</h3>
              <p className="text-gray-400 text-sm">
                Comprehensive sculpture materials including Bronze, Marble, Clay, and Mixed Media 
                for authentic set decoration and prop design.
              </p>
            </div>
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-3">📺 TV Production</h3>
              <p className="text-gray-400 text-sm">
                Specialized TV styles including Sitcom Living Room, Medical Drama, Crime Procedural, 
                and Reality TV Loft for consistent episodic design.
              </p>
            </div>
            <div className="bg-gray-800/30 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-3">🎨 Production Materials</h3>
              <p className="text-gray-400 text-sm">
                100+ production materials across 10 categories including traditional materials, 
                synthetic options, and artistic elements for complete set design.
              </p>
            </div>
          </div>
        </div>

        {/* Cinematic Style Categories */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">🎭 Style Categories for Production Design</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-teal-900/20 border border-teal-700 rounded-lg p-4">
              <h3 className="text-teal-400 font-semibold mb-2">🎬 Cinematic & TV/Film</h3>
              <p className="text-gray-300 text-sm">Period pieces, genre-specific styles, TV show aesthetics</p>
            </div>
            <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
              <h3 className="text-blue-400 font-semibold mb-2">🌍 Cultural</h3>
              <p className="text-gray-300 text-sm">International styles, regional variations, authentic cultural design</p>
            </div>
            <div className="bg-purple-900/20 border border-purple-700 rounded-lg p-4">
              <h3 className="text-purple-400 font-semibold mb-2">🎨 Eclectic</h3>
              <p className="text-gray-300 text-sm">Artistic styles, mixed media, creative combinations</p>
            </div>
            <div className="bg-green-900/20 border border-green-700 rounded-lg p-4">
              <h3 className="text-green-400 font-semibold mb-2">🌿 Eco & Nature</h3>
              <p className="text-gray-300 text-sm">Sustainable design, natural materials, biophilic elements</p>
            </div>
            <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-4">
              <h3 className="text-yellow-400 font-semibold mb-2">✨ Luxury</h3>
              <p className="text-gray-300 text-sm">High-end materials, glamorous finishes, sophisticated drama</p>
            </div>
            <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
              <h3 className="text-red-400 font-semibold mb-2">🏛️ Traditional</h3>
              <p className="text-gray-300 text-sm">Classic styles, historical accuracy, timeless elegance</p>
            </div>
          </div>
        </div>

        {/* Style Selector */}
        <div className="bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">🎬 Professional Style Selector</h2>
            <p className="text-gray-400">
              Choose from 110+ authentic styles for your production. Filter by category and select multiple styles 
              for complex scenes or mixed aesthetic requirements.
            </p>
          </div>
          <StyleCardSelector
            selectedStyles={selectedStyles}
            onStyleToggle={handleStyleToggle}
            maxSelection={8}
          />
        </div>

        {/* Selected Styles Summary */}
        {selectedStyles.length > 0 && (
          <div className="mt-8 bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
            <h3 className="text-xl font-semibold text-white mb-4">
              🎭 Selected Production Styles ({selectedStyles.length})
            </h3>
            <p className="text-gray-400 text-sm mb-4">
              Your selected styles for this production. These will be used to generate authentic set designs 
              and material recommendations for your TV/film project.
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedStyles.map(styleId => (
                <span 
                  key={styleId}
                  className="bg-teal-900/30 border border-teal-700 text-teal-300 px-3 py-1 rounded-full text-sm"
                >
                  Style #{styleId}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-12 text-center bg-gradient-to-r from-teal-900/30 to-blue-900/30 rounded-2xl p-8 border border-teal-700/50">
          <h2 className="text-2xl font-bold text-white mb-4">🎬 Ready for Production?</h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Start creating authentic, professional set designs for your TV series, movies, or entertainment productions. 
            Our comprehensive design system ensures historical accuracy and visual consistency across your project.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
              🎭 Start Set Design
            </button>
            <button className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
              📚 View Style Guide
            </button>
            <button className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
              🏗️ Browse Materials
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}
