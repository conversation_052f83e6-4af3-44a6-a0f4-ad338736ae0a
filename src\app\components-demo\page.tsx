"use client";
import React, { useState } from 'react';
import RoomTypeSelector from '../../components/RoomTypeSelector';
import StyleSelector from '../../components/StyleSelector';
import ColorPaletteSelector from '../../components/ColorPaletteSelector';
import MaterialSelector, { SAMPLE_MATERIALS } from '../../components/MaterialSelector';
import ImageUploader from '../../components/ImageUploader';

export default function ComponentsDemoPage() {
  // State for all components
  const [selectedRoomType, setSelectedRoomType] = useState<string | null>(null);
  const [selectedStyles, setSelectedStyles] = useState<number[]>([]);
  const [selectedPalettes, setSelectedPalettes] = useState<string[]>([]);
  const [selectedMaterials, setSelectedMaterials] = useState<number[]>([]);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);

  const handleStyleToggle = (styleId: number) => {
    setSelectedStyles(prev => 
      prev.includes(styleId)
        ? prev.filter(id => id !== styleId)
        : [...prev, styleId]
    );
  };

  const handlePaletteToggle = (paletteId: string) => {
    setSelectedPalettes(prev =>
      prev.includes(paletteId)
        ? prev.filter(id => id !== paletteId)
        : [...prev, paletteId]
    );
  };

  const handleMaterialToggle = (materialId: number) => {
    setSelectedMaterials(prev =>
      prev.includes(materialId)
        ? prev.filter(id => id !== materialId)
        : [...prev, materialId]
    );
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🎬 Production Design Studio
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Professional design tools for TV series, movies, and entertainment productions.
            Create authentic sets, period pieces, and cinematic environments with our comprehensive design system.
          </p>
          <div className="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-400">
            <span className="bg-gray-800 px-3 py-1 rounded-full">🎭 Set Design</span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">🎥 Film Production</span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">📺 TV Series</span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">🎪 Theater</span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">🏛️ Period Pieces</span>
          </div>
        </div>

        <div className="space-y-16">
          {/* Room Type Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">🎬 Set Location Selector</h2>
              <p className="text-gray-400">
                Choose from 12 different set locations with visual icons and descriptions. Perfect for film and TV production design.
              </p>
            </div>
            <RoomTypeSelector
              selectedRoomType={selectedRoomType}
              onRoomTypeSelect={setSelectedRoomType}
            />
          </section>

          {/* Style Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">🎭 Production Style Selector</h2>
              <p className="text-gray-400">
                Multi-select from 110+ design styles including period pieces, cinematic genres, and cultural styles. Perfect for TV/film production design with authentic period accuracy.
              </p>
            </div>
            <StyleSelector
              selectedStyles={selectedStyles}
              onStyleToggle={handleStyleToggle}
              maxSelection={5}
            />
          </section>

          {/* Color Palette Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Color Palette Selector</h2>
              <p className="text-gray-400">
                50+ curated color palettes organized by category with live color swatches and custom color options.
              </p>
            </div>
            <ColorPaletteSelector
              selectedPalettes={selectedPalettes}
              onPaletteToggle={handlePaletteToggle}
              maxSelection={8}
            />
          </section>

          {/* Material Selector Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">🏗️ Production Materials & Props</h2>
              <p className="text-gray-400">
                Comprehensive material library including sculpture options for set decoration. 100+ materials across 10 categories including authentic period materials.
              </p>
            </div>
            <MaterialSelector
              materials={SAMPLE_MATERIALS}
              selectedMaterials={selectedMaterials}
              onMaterialToggle={handleMaterialToggle}
              maxSelection={5}
            />
          </section>

          {/* Image Uploader Demo */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Image Uploader</h2>
              <p className="text-gray-400">
                Drag-and-drop image upload with validation, preview, and helpful tips.
              </p>
            </div>
            <ImageUploader
              selectedImage={selectedImage}
              onImageSelect={setSelectedImage}
            />
          </section>

          {/* Selection Summary */}
          <section className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Current Selections</h2>
              <p className="text-gray-400">
                Summary of all your current selections across components.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Room Type</h3>
                <p className="text-gray-400 text-sm">
                  {selectedRoomType || 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Styles</h3>
                <p className="text-gray-400 text-sm">
                  {selectedStyles.length > 0 ? `${selectedStyles.length} selected` : 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Color Palettes</h3>
                <p className="text-gray-400 text-sm">
                  {selectedPalettes.length > 0 ? `${selectedPalettes.length} selected` : 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Materials</h3>
                <p className="text-gray-400 text-sm">
                  {selectedMaterials.length > 0 ? `${selectedMaterials.length} selected` : 'None selected'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Image</h3>
                <p className="text-gray-400 text-sm">
                  {selectedImage ? selectedImage.name : 'None uploaded'}
                </p>
              </div>

              <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                <h3 className="text-white font-medium mb-2">Ready to Generate</h3>
                <p className="text-gray-400 text-sm">
                  {selectedRoomType && selectedStyles.length > 0 && selectedPalettes.length > 0 && selectedMaterials.length > 0
                    ? '✅ All set!'
                    : '⏳ Select more options'
                  }
                </p>
              </div>
            </div>
          </section>

          {/* Navigation */}
          <section className="text-center">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/design/new"
                className="bg-teal-600 hover:bg-teal-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                Try the Full Design Wizard
              </a>
              <a
                href="/eco-styles-demo"
                className="bg-green-600 hover:bg-green-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                🌿 Eco-Design Styles
              </a>
              <a
                href="/materials-demo"
                className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                View Materials Demo
              </a>
              <a
                href="/"
                className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                Back to Home
              </a>
            </div>
          </section>
        </div>
      </div>
    </main>
  );
}
