"use client";
import React, { useState } from 'react';
import DesignWizard from '../../../components/DesignWizard';

interface DesignConfig {
  mode: 'restyle' | 'generate';
  roomType: string | null;
  styles: number[];
  colorPalettes: string[];
  materials: number[];
  image: File | null;
}

export default function NewDesignPage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedResults, setGeneratedResults] = useState<string[]>([]);

  const handleDesignComplete = async (config: DesignConfig) => {
    console.log('Design configuration:', config);
    setIsGenerating(true);

    // Simulate AI generation process
    try {
      // Mock API call - replace with actual AI service
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock generated results
      const mockResults = [
        "https://ext.same-assets.com/4123950039/3970137075.jpeg",
        "https://ext.same-assets.com/4123950039/2719286588.jpeg",
        "https://ext.same-assets.com/4123950039/1267053207.jpeg"
      ];
      
      setGeneratedResults(mockResults);
    } catch (error) {
      console.error('Generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleStartOver = () => {
    setGeneratedResults([]);
    setIsGenerating(false);
  };

  if (isGenerating) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center">
            <div className="mb-8">
              <div className="w-16 h-16 border-4 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
              <h1 className="text-3xl font-bold text-white mb-4">Generating Your Design</h1>
              <p className="text-gray-400 text-lg">
                Our AI is creating beautiful designs based on your preferences...
              </p>
              <p className="text-teal-400 text-sm mt-2">
                This usually takes 30-60 seconds
              </p>
            </div>

            <div className="bg-gray-800/30 rounded-xl p-6 border border-gray-700 max-w-md mx-auto">
              <h3 className="text-white font-medium mb-3">Processing Steps</h3>
              <div className="space-y-2 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                  Analyzing your preferences
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-teal-500 rounded-full animate-pulse"></div>
                  Generating design variations
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
                  Applying finishing touches
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    );
  }

  if (generatedResults.length > 0) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-4">Your AI-Generated Designs</h1>
            <p className="text-gray-400 text-lg">
              Here are your personalized room designs. Click on any image to view in full size.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {generatedResults.map((imageUrl, index) => (
              <div key={index} className="bg-gray-900 rounded-xl border border-gray-700 overflow-hidden hover:border-teal-500 transition-all">
                <img
                  src={imageUrl}
                  alt={`Generated design ${index + 1}`}
                  className="w-full h-64 object-cover"
                />
                <div className="p-4">
                  <h3 className="text-white font-medium mb-2">Design Variation {index + 1}</h3>
                  <div className="flex gap-2">
                    <button className="flex-1 bg-teal-600 hover:bg-teal-500 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                      Download
                    </button>
                    <button className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
                      Save
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleStartOver}
                className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
              >
                Create New Design
              </button>
              <button className="bg-teal-600 hover:bg-teal-500 text-white py-3 px-6 rounded-lg font-medium transition-colors">
                Generate More Variations
              </button>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🎬 Create Professional Production Design
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Use Altora's AI-powered design wizard to create authentic sets for TV, film, and entertainment productions.
            Choose your style, colors, materials, and let our AI create cinematic environments.
          </p>
        </div>

        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8">
          <DesignWizard onComplete={handleDesignComplete} />
        </div>
      </div>
    </main>
  );
}
