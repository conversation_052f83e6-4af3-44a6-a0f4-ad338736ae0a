"use client";
import { useState } from 'react';
import StyleSelector, { STYLES } from '../../components/StyleSelector';

export default function EcoStylesDemoPage() {
  const [selectedStyles, setSelectedStyles] = useState<number[]>([]);

  const handleStyleToggle = (styleId: number) => {
    setSelectedStyles(prev => 
      prev.includes(styleId)
        ? prev.filter(id => id !== styleId)
        : [...prev, styleId]
    );
  };

  // Filter eco and nature styles
  const ecoStyles = STYLES.filter(style => style.category === 'eco');
  const allStyles = STYLES;

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🌿 Eco-Design & Nature Styles
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Discover sustainable and nature-inspired design styles that bring the outdoors in. 
            From biophilic design to urban jungles, create spaces that connect with nature.
          </p>
        </div>

        {/* Eco Styles Showcase */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">Eco & Nature Collection ({ecoStyles.length} styles)</h2>
            <p className="text-gray-400">
              Sustainable design styles that prioritize environmental consciousness and natural elements.
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {ecoStyles.map((style) => {
              const isSelected = selectedStyles.includes(style.id);
              
              return (
                <button
                  key={style.id}
                  onClick={() => handleStyleToggle(style.id)}
                  className={`
                    group flex flex-col items-stretch justify-end rounded-xl border-2 overflow-hidden bg-gray-900 transition-all h-40 relative shadow-md
                    ${isSelected 
                      ? 'border-green-500 ring-4 ring-green-300/40' 
                      : 'border-gray-700 hover:border-green-500 hover:z-10 scale-100 hover:scale-105 active:scale-95 duration-150'
                    }
                  `}
                  title={style.label}
                >
                  <img
                    src={style.image}
                    alt={style.label}
                    className="object-cover w-full h-28 group-hover:scale-105 duration-300 rounded-t-xl"
                    draggable={false}
                  />
                  <span
                    className={`px-3 py-2 text-sm font-semibold text-center truncate ${
                      isSelected ? "text-green-400" : "text-white"
                    }`}
                  >
                    {style.label}
                  </span>

                  {/* Selection Indicator */}
                  {isSelected && (
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8.586 10l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Eco Style Descriptions - Using Database Schema Descriptions */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Eco-Design Style Guide</h2>
          <p className="text-gray-400 mb-6">
            All descriptions are sourced directly from the database schema to ensure consistency.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {ecoStyles.map((style) => (
              <div key={style.id} className="bg-gray-800/30 rounded-lg p-4 border border-gray-700">
                <h3 className="text-green-400 font-medium mb-2">🌱 {style.label}</h3>
                <p className="text-gray-400 text-sm">
                  {style.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Full Style Selector */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-2">All Design Styles ({allStyles.length} total)</h2>
            <p className="text-gray-400">
              Browse all available design styles including the new eco-friendly options.
            </p>
          </div>
          <StyleSelector
            selectedStyles={selectedStyles}
            onStyleToggle={handleStyleToggle}
            maxSelection={10}
          />
        </div>

        {/* Benefits of Eco Design */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Benefits of Eco-Design</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Health & Wellness</h3>
              <p className="text-gray-400 text-sm">
                Improves air quality, reduces stress, and creates healthier living environments through natural elements.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Environmental Impact</h3>
              <p className="text-gray-400 text-sm">
                Reduces carbon footprint through sustainable materials and energy-efficient design choices.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm3.5-9c.83 0 1.5-.67 1.5-1.5S16.33 8 15.5 8 14 8.67 14 9.5s.67 1.5 1.5 1.5zm-7 0c.83 0 1.5-.67 1.5-1.5S9.33 8 8.5 8 7 8.67 7 9.5 7.67 11 8.5 11zm3.5 6.5c2.33 0 4.31-1.46 5.11-3.5H6.89c.8 2.04 2.78 3.5 5.11 3.5z"/>
                </svg>
              </div>
              <h3 className="text-white font-medium mb-2">Cost Savings</h3>
              <p className="text-gray-400 text-sm">
                Long-term savings through energy efficiency, durable materials, and reduced maintenance needs.
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/design/new"
              className="bg-green-600 hover:bg-green-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Create Eco-Friendly Design
            </a>
            <a
              href="/components-demo"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              View All Components
            </a>
            <a
              href="/"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Back to Home
            </a>
          </div>
        </div>
      </div>
    </main>
  );
}
