"use client";

const FAQS = [
  {
    q: "What is altora?",
    a: "altora is an AI-powered interior design platform that helps homeowners, renters, designers, and professionals create stunning room designs in minutes. Simply upload a photo of your space and let our AI transform it with over 110+ professional design styles."
  },
  {
    q: "Who can use altora?",
    a: "altora is designed for everyone - from homeowners wanting to redesign their living space to professional interior designers, real estate agents, and architects. Whether you're planning a renovation, staging a home, or just exploring design ideas, altora makes professional design accessible to all."
  },
  {
    q: "How does the AI design process work?",
    a: "Our advanced AI analyzes your room photo, understands the space layout, furniture placement, and lighting, then applies your chosen design style while maintaining the room's structure and proportions. The process typically takes 30-60 seconds to generate multiple high-quality design variations."
  },
  {
    q: "What types of rooms and spaces can I design?",
    a: "You can design any interior space including living rooms, bedrooms, kitchens, bathrooms, home offices, dining rooms, nurseries, basements, garages, laundry rooms, attics, walk-in closets, and more. Our AI works with both residential and commercial spaces of any size."
  },
  {
    q: "How many design styles are available?",
    a: "We offer 110+ professionally curated design styles including Modern, Scandinavian, Industrial, Bohemian, Traditional, Minimalist, Mid-Century Modern, Farmhouse, Art Deco, and many more. Each style includes detailed descriptions and is crafted by professional interior designers."
  },
  {
    q: "Can I customize colors and materials?",
    a: "Yes! altora offers extensive customization options including 12+ color palettes (from Soft Neutrals to Golden Hour), 20+ material options (wood, metal, stone, glass, fabrics), and the ability to mix and match different elements to create your perfect design."
  },
  {
    q: "What image quality do I need for best results?",
    a: "For optimal results, use high-resolution images (at least 1024x1024 pixels) with good lighting and clear visibility of the room. We support JPG, PNG, and WebP formats. Well-lit photos with minimal clutter produce the most accurate and impressive transformations."
  },
  {
    q: "Is altora suitable for professional use?",
    a: "Absolutely! Many interior designers use altora to quickly generate design concepts for clients, real estate agents use it for virtual staging, and architects use it for presentation materials. Our Pro plans include commercial usage rights and high-resolution outputs perfect for professional presentations."
  },
  {
    q: "How secure is my data and images?",
    a: "We take privacy seriously. All images are processed using enterprise-grade security measures, encrypted during transmission and storage, and are never shared with third parties. You maintain full ownership of your images and can delete them at any time."
  },
  {
    q: "Can I try altora for free?",
    a: "Yes! Our free plan includes 3 design generations per month, access to 5 popular design styles, and standard resolution outputs. It's perfect for exploring the platform and small personal projects before upgrading to a paid plan."
  },
  {
    q: "What's the difference between plans?",
    a: "Our Free plan offers basic features, Pro plan includes unlimited generations and all 110+ styles, and Enterprise plan adds commercial rights, priority support, and team collaboration features. All plans include our core AI technology and regular style updates."
  },
  {
    q: "Can I use altora designs for renovation planning?",
    a: "Yes! Many users use altora to visualize renovation ideas, experiment with different styles before committing to purchases, and share design concepts with contractors or family members. It's a cost-effective way to explore possibilities before making expensive design decisions."
  }
];

export default function FAQPage() {
  return (
    <main className="max-w-3xl mx-auto px-4 py-16">
      <h1 className="text-3xl md:text-5xl font-bold text-white mb-10">Your questions, answered</h1>
      <section className="space-y-4">
        {FAQS.map((faq, i) => (
          <div key={i} className="rounded-xl border-2 border-gray-800 bg-gray-950 px-6 py-5">
            <div className="font-bold text-lg text-teal-400 mb-2">{faq.q}</div>
            <div className="text-gray-300 text-base whitespace-pre-line">{faq.a}</div>
          </div>
        ))}
      </section>
    </main>
  );
}
