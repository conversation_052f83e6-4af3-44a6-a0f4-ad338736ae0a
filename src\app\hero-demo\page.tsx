"use client";
import React from 'react';
import HeroSection from '../../components/HeroSection';

export default function HeroDemoPage() {
  return (
    <main className="min-h-screen">
      <HeroSection 
        title="Professional Production Design"
        subtitle="Create authentic sets for TV series, movies, and entertainment productions. Professional design tools with 110+ styles and cinematic environments."
        primaryButtonText="Start Designing"
        primaryButtonHref="/design/new"
        secondaryButtonText="View Gallery"
        secondaryButtonHref="/gallery"
      />
      
      {/* Additional content to show the hero works with other sections */}
      <section className="bg-gray-900 py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Hero Section Demo
          </h2>
          <p className="text-gray-300 text-lg">
            This page demonstrates the new hero section component with:
          </p>
          <ul className="text-gray-300 mt-4 space-y-2">
            <li>• Split layout with content on the left and scrolling images on the right</li>
            <li>• Seamless infinite scrolling animations (top row left, bottom row right)</li>
            <li>• Responsive design for desktop and mobile</li>
            <li>• Smooth animations and visual styling</li>
            <li>• Gradient overlays for polished edges</li>
          </ul>
        </div>
      </section>
    </main>
  );
}
