"use client";
import React, { useState } from 'react';
import MaterialSelector, { SAMPLE_MATERIALS } from '../../components/MaterialSelector';
import { getUniqueMaterialIcon } from '../../components/icons/UniqueMaterialIcons';

export default function MaterialsDemoPage() {
  const [selectedMaterials, setSelectedMaterials] = useState<number[]>([]);

  const handleMaterialToggle = (materialId: number) => {
    setSelectedMaterials(prev =>
      prev.includes(materialId)
        ? prev.filter(id => id !== materialId)
        : [...prev, materialId]
    );
  };

  // Group materials by category for statistics
  const materialsByCategory = SAMPLE_MATERIALS.reduce((acc, material) => {
    if (!acc[material.category]) {
      acc[material.category] = [];
    }
    acc[material.category].push(material);
    return acc;
  }, {} as Record<string, typeof SAMPLE_MATERIALS>);

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-slate-950 py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            🎬 Production Materials & Props Library
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Comprehensive collection of {SAMPLE_MATERIALS.length} production materials across 10 categories including sculpture options.
            Perfect for set decoration, prop design, and authentic period productions. From traditional materials to artistic sculptures.
          </p>
          <div className="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-400">
            <span className="bg-gray-800 px-3 py-1 rounded-full">🎭 Set Decoration</span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">🗿 Sculpture Props</span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">🏛️ Period Materials</span>
            <span className="bg-gray-800 px-3 py-1 rounded-full">🎨 Artistic Elements</span>
          </div>
        </div>

        {/* Materials Statistics */}
        <div className="bg-gray-900/50 rounded-2xl border border-gray-800 p-8 mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Material Categories Overview</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {Object.entries(materialsByCategory).map(([category, materials]) => (
              <div key={category} className="bg-gray-800/30 rounded-lg p-4 border border-gray-700 text-center">
                <div className="text-2xl font-bold text-teal-400 mb-2">{materials.length}</div>
                <div className="text-gray-300 font-medium">{category}</div>
                <div className="text-xs text-gray-500 mt-1">Materials</div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">{SAMPLE_MATERIALS.length}</div>
            <div className="text-gray-300 text-sm">Total Materials</div>
          </div>
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">10</div>
            <div className="text-gray-300 text-sm">Categories</div>
          </div>
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">{selectedMaterials.length}</div>
            <div className="text-gray-300 text-sm">Selected</div>
          </div>
          <div className="bg-gray-800/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-teal-400">SVG</div>
            <div className="text-gray-300 text-sm">Icon Format</div>
          </div>
        </div>

        {/* Material Selector */}
        <div className="bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
          <MaterialSelector
            materials={SAMPLE_MATERIALS}
            selectedMaterials={selectedMaterials}
            onMaterialToggle={handleMaterialToggle}
            maxSelection={10}
          />
        </div>

        {/* Selected Materials Summary */}
        {selectedMaterials.length > 0 && (
          <div className="mt-8 bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
            <h3 className="text-xl font-semibold text-white mb-4">
              Selected Materials ({selectedMaterials.length})
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {selectedMaterials.map(materialId => {
                const material = SAMPLE_MATERIALS.find(m => m.id === materialId);
                if (!material) return null;

                return (
                  <div
                    key={materialId}
                    className="bg-gray-700/50 rounded-lg p-3 border border-gray-600 flex items-center space-x-3"
                  >
                    <div className="w-8 h-8 flex items-center justify-center">
                      {getUniqueMaterialIcon(material.name, 32, "rounded")}
                    </div>
                    <div>
                      <div className="text-white font-medium text-sm">{material.name}</div>
                      <div className="text-gray-400 text-xs">{material.category}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Database Schema Information */}
        <div className="mt-12 bg-gray-800/30 rounded-2xl p-6 border border-gray-700">
          <h3 className="text-xl font-semibold text-white mb-4">
            ✅ Complete Database Schema Implementation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-white font-medium mb-3">Material Categories</h4>
              <div className="space-y-2">
                {Object.entries(materialsByCategory).map(([category, materials]) => (
                  <div key={category} className="flex justify-between text-sm">
                    <span className="text-gray-300">{category}</span>
                    <span className="text-teal-400 font-medium">{materials.length} materials</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-white font-medium mb-3">Implementation Status</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-300">Total Materials</span>
                  <span className="text-green-400 font-medium">✅ {SAMPLE_MATERIALS.length}/87</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Categories</span>
                  <span className="text-green-400 font-medium">✅ 9/9</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Icons</span>
                  <span className="text-green-400 font-medium">✅ SVG Icons</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Database Ready</span>
                  <span className="text-green-400 font-medium">✅ Schema Match</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-12 text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/design"
              className="bg-teal-600 hover:bg-teal-500 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Use in Design Wizard
            </a>
            <a
              href="/style-cards-demo"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              🎨 Style Cards Demo
            </a>
            <a
              href="/components-demo"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              View All Components
            </a>
            <a
              href="/"
              className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-lg font-medium transition-colors"
            >
              Back to Home
            </a>
          </div>
        </div>
      </div>
    </main>
  );
}
