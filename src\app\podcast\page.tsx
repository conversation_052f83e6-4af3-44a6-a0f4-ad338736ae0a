"use client";
const EPISODES = [
  { title: "AI and the Future of Interior Design", date: "July 2025", desc: "How AI is reshaping the way we think about space, color, and mood." },
  { title: "Understanding Design Styles", date: "June 2025", desc: "A deep dive into the most popular interior design styles and how to use them." },
  { title: "The Power of Moodboards", date: "May 2025", desc: "Tips for creating visually compelling moodboards using digital or physical tools." }
];
export default function PodcastPage() {
  return (
    <main className="max-w-4xl mx-auto px-4 py-16">
      <h1 className="text-3xl md:text-5xl font-bold text-white mb-3">Interior Design Podcast</h1>
      <div className="bg-teal-900/70 border-2 border-teal-700 rounded-xl p-6 flex flex-col items-center mb-10">
        <p className="text-lg text-white">Listen to industry insights and creative talks about modern interior design, AI, and practical advice.</p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        {EPISODES.map(ep => (
          <div key={ep.title} className="bg-gray-900 border-2 border-gray-800 rounded-xl p-6 flex flex-col gap-2 shadow group hover:border-teal-500 transition">
            <div className="font-bold text-white text-lg">{ep.title}</div>
            <div className="text-teal-400 text-sm mb-1">{ep.date}</div>
            <div className="text-gray-300 text-base">{ep.desc}</div>
          </div>
        ))}
      </div>
    </main>
  );
}
