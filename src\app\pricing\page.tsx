"use client";
export default function PricingPage() {
  return (
    <main className="max-w-4xl mx-auto px-4 py-16">
      <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">Start creating professional production designs with Altora AI</h1>
      <div className="mt-4 flex gap-3 items-center text-sm">
        <button className="px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg font-semibold text-white hover:bg-gray-700">Yearly <span className="ml-1 text-xs bg-teal-600 text-white px-2 py-0.5 rounded-full">33% off</span></button>
        <button className="px-4 py-2 bg-gray-900 border border-gray-700 rounded-lg font-semibold text-gray-300 hover:bg-gray-800">Monthly</button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-10">
        {/* Personal */}
        <div className="bg-gray-900 rounded-xl border-2 border-gray-700 shadow-lg flex flex-col items-start p-7 gap-4 relative">
          <div className="absolute right-4 top-4 text-sm text-gray-600">Personal</div>
          <span className="text-3xl font-bold text-white mb-2">$19<span className="text-lg ml-1 font-medium text-gray-300">/month</span></span>
          <div className="mb-1 text-gray-300">Billed $228 yearly<br /><span className="text-xs">Cancel anytime</span></div>
          <ul className="mt-2 mb-2 text-base text-gray-300 space-y-1 pl-4 list-disc">
            <li>250 images per month</li>
            <li>Personal-use only</li>
            <li>Small watermark</li>
            <li>Normal resolution</li>
            <li>1 user</li>
          </ul>
          <button className="w-full mt-auto px-5 py-2 rounded-lg bg-teal-600 text-white font-semibold text-lg border border-teal-400 hover:bg-teal-500 hover:border-teal-300 transition">Select</button>
        </div>
        {/* Pro */}
        <div className="bg-gray-950 rounded-xl border-2 border-teal-500 shadow-lg flex flex-col items-start p-7 gap-4 relative scale-105 z-10">
          <div className="absolute right-4 top-4 text-xs text-white bg-teal-600 px-3 py-1 rounded-lg font-bold">Most popular</div>
          <div className="absolute left-4 top-4 text-sm text-gray-600">Pro</div>
          <span className="text-3xl font-bold text-white mb-2">$69<span className="text-lg ml-1 font-medium text-gray-300">/month</span></span>
          <div className="mb-1 text-gray-300">Billed $828 yearly<br /><span className="text-xs">Cancel anytime</span></div>
          <ul className="mt-2 mb-2 text-base text-gray-300 space-y-1 pl-4 list-disc">
            <li>1,000 images per month</li>
            <li>Commercial license</li>
            <li>No watermark</li>
            <li>Highest resolution</li>
            <li>1 user</li>
          </ul>
          <button className="w-full mt-auto px-5 py-2 rounded-lg bg-teal-600 text-white font-semibold text-lg border border-teal-400 hover:bg-teal-500 hover:border-teal-300 transition">Select</button>
        </div>
        {/* Team */}
        <div className="bg-gray-900 rounded-xl border-2 border-gray-700 shadow-lg flex flex-col items-start p-7 gap-4 relative">
          <div className="absolute right-4 top-4 text-sm text-gray-600">Team</div>
          <span className="text-3xl font-bold text-white mb-2">$199<span className="text-lg ml-1 font-medium text-gray-300">/month</span></span>
          <div className="mb-1 text-gray-300">Billed $2,388 yearly<br /><span className="text-xs">Cancel anytime</span></div>
          <ul className="mt-2 mb-2 text-base text-gray-300 space-y-1 pl-4 list-disc">
            <li>5,000 images per month</li>
            <li>Commercial license</li>
            <li>No watermark</li>
            <li>Highest resolution</li>
            <li>Up to 5 users</li>
            <li>Train your own style</li>
          </ul>
          <button className="w-full mt-auto px-5 py-2 rounded-lg bg-teal-600 text-white font-semibold text-lg border border-teal-400 hover:bg-teal-500 hover:border-teal-300 transition">Select</button>
        </div>
      </div>
    </main>
  );
}
