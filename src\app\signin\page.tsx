"use client";
import { AltoraLogo } from "../../components/AltoraLogo";
import Link from "next/link";

export default function SignInPage() {
  return (
    <main className="flex flex-col items-center justify-center min-h-[70vh]">
      <section className="bg-gray-900 border-2 border-gray-800 rounded-3xl shadow-2xl max-w-md w-full p-10 md:mt-8 mb-8 flex flex-col items-center">
        <div className="mb-7">
          <AltoraLogo size="lg" />
        </div>
        <h1 className="font-medium text-2xl text-gray-300 mb-7 text-center">
          Welcome back to Altora
        </h1>
        
        <div className="flex flex-col gap-3 w-full mb-7">
          <button className="text-base font-medium flex items-center justify-center w-full bg-gray-300 text-gray-900 gap-3 py-2 rounded-lg ring-1 ring-inset ring-white shadow-md hover:bg-white transition">
            <img className="h-6 w-6 object-contain" src="https://ext.same-assets.com/4123950039/3172151841.svg" alt="Google" />
            Continue with Google
          </button>
        </div>
        
        <div className="relative flex h-10 items-center my-3">
          <hr className="grow border-gray-800 h-[2px]" />
          <span className="w-10 shrink-0 text-center uppercase text-sm text-gray-500 tracking-wider">or</span>
          <hr className="grow border-gray-800 h-[2px]" />
        </div>
        
        <form className="w-full">
          <input 
            type="email" 
            required 
            placeholder="Enter your email address" 
            className="bg-gray-800 w-full text-base rounded-lg px-3 py-2 placeholder:text-gray-500 border border-gray-700 focus:border-teal-500 focus:ring-teal-500 transition-colors text-white mb-4" 
          />
          <input 
            type="password" 
            placeholder="Password (optional)" 
            className="bg-gray-800 w-full text-base rounded-lg px-3 py-2 placeholder:text-gray-500 border border-gray-700 focus:border-teal-500 focus:ring-teal-500 transition-colors text-white" 
          />
          <p className="text-xs text-gray-500 mt-2 mb-4">
            Leave password blank to receive a magic link instead
          </p>
          <button type="submit" className="w-full font-medium bg-teal-700 ring-1 ring-inset ring-teal-500 cursor-pointer text-base rounded-lg px-4 py-2 text-white hover:bg-teal-600 hover:ring-teal-500 transition">
            Sign In
          </button>
        </form>
        
        <div className="mt-4 text-center">
          <Link href="/forgot-password" className="text-sm text-teal-400 hover:text-teal-300 underline">
            Forgot your password?
          </Link>
        </div>
        
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-400">
            Don't have an account?{" "}
            <Link href="/signup" className="text-teal-400 hover:text-teal-300 underline">
              Sign up
            </Link>
          </p>
        </div>
      </section>
    </main>
  );
}
