"use client";
import React, { useState } from "react";

const STYLES = [
  { label: "Minimalist", image: "https://ext.same-assets.com/4123950039/1267053207.jpeg" },
  { label: "Scandinavian", image: "https://ext.same-assets.com/4123950039/4031576135.jpeg" },
  { label: "Industrial", image: "https://ext.same-assets.com/4123950039/2311013961.jpeg" },
  { label: "Mid-Century Modern", image: "https://ext.same-assets.com/4123950039/4247774748.jpeg" },
  { label: "Bohemian", image: "https://ext.same-assets.com/4123950039/2162116070.jpeg" },
  { label: "Art Deco", image: "https://ext.same-assets.com/4123950039/3217488218.jpeg" },
  { label: "Coastal", image: "https://ext.same-assets.com/4123950039/4253859114.jpeg" },
  { label: "Farmhouse", image: "https://ext.same-assets.com/4123950039/2462314040.jpeg" },
  { label: "Traditional", image: "https://ext.same-assets.com/4123950039/362001557.jpeg" },
  { label: "French Country", image: "https://ext.same-assets.com/4123950039/690654409.jpeg" },
  { label: "Moroccan", image: "https://ext.same-assets.com/4123950039/2521446536.jpeg" },
  { label: "Modern", image: "https://ext.same-assets.com/4123950039/1390157197.jpeg" },
  { label: "Transitional", image: "https://ext.same-assets.com/4123950039/891312444.jpeg" },
  { label: "Shabby Chic", image: "https://ext.same-assets.com/4123950039/1011160405.jpeg" },
  { label: "Japanese Zen", image: "https://ext.same-assets.com/4123950039/3933773814.jpeg" },
  { label: "Contemporary", image: "https://ext.same-assets.com/4123950039/3246906598.jpeg" },
  { label: "Gothic", image: "https://ext.same-assets.com/4123950039/1038660028.jpeg" },
  { label: "Hollywood Regency", image: "https://ext.same-assets.com/4123950039/927860819.jpeg" },
  { label: "Rustic", image: "https://ext.same-assets.com/4123950039/1650356877.jpeg" },
  { label: "Southwestern", image: "https://ext.same-assets.com/4123950039/2422832776.jpeg" },
  { label: "Victorian", image: "https://ext.same-assets.com/4123950039/180188305.jpeg" },
  { label: "Steampunk", image: "https://ext.same-assets.com/4123950039/650580268.jpeg" },
  { label: "Biophilic", image: "https://ext.same-assets.com/4123950039/3597336419.jpeg" },
  { label: "Futuristic", image: "https://ext.same-assets.com/4123950039/1432023951.jpeg" },
  { label: "Maximalist", image: "https://ext.same-assets.com/4123950039/1389072092.jpeg" },
  { label: "Pop Art", image: "https://ext.same-assets.com/4123950039/3540851178.jpeg" },
  { label: "Cosmic Chic", image: "https://ext.same-assets.com/4123950039/3420677188.jpeg" },
  { label: "Wabi-Sabi", image: "https://ext.same-assets.com/4123950039/744976908.jpeg" },
  { label: "Tropical", image: "https://ext.same-assets.com/4123950039/2401809942.jpeg" },
  { label: "Vintage Glam", image: "https://ext.same-assets.com/4123950039/1246017528.jpeg" },
  { label: "Cyberpunk", image: "https://ext.same-assets.com/4123950039/2253538115.jpeg" },
  { label: "Psychedelic", image: "https://ext.same-assets.com/4123950039/3683077406.jpeg" },
  { label: "Surrealist", image: "https://ext.same-assets.com/4123950039/1098851357.jpeg" },
  { label: "Post-Apocalyptic", image: "https://ext.same-assets.com/4123950039/576171152.jpeg" },
  { label: "Candy Land", image: "https://ext.same-assets.com/4123950039/3193769849.jpeg" }
];

export default function TryDemoPage() {
  const [selected, setSelected] = useState<string[]>([]);
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);

  function toggle(style: string) {
    setSelected((list) =>
      list.includes(style)
        ? list.filter((s) => s !== style)
        : list.length < 5
        ? [...list, style]
        : list
    );
  }

  function onFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    const f = e.target.files?.[0] || null;
    setFile(f);
    if (f) setPreview(URL.createObjectURL(f));
    else setPreview(null);
  }

  return (
    <main className="max-w-5xl mx-auto px-4 pt-12">
      <h2 className="text-2xl md:text-3xl font-bold text-gray-50 mb-6">What's your design style? <span className="font-normal text-base text-gray-400">You can choose up to 5 styles</span></h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 mb-8">
        {STYLES.map(({ label, image }) => (
          <button
            key={label}
            onClick={() => toggle(label)}
            className={`group flex flex-col items-stretch justify-end rounded-xl border-2 overflow-hidden bg-gray-900 transition-all h-40 relative shadow-md ${selected.includes(label)
              ? "border-teal-500 ring-4 ring-teal-300/40"
              : "border-gray-700 hover:border-teal-500 hover:z-10"}`}
            aria-pressed={selected.includes(label)}
            type="button"
          >
            <img src={image} alt={label} className="object-cover w-full h-28 group-hover:scale-105 duration-300" draggable={false} />
            <span className={`px-3 py-1 text-base font-semibold text-left truncate ${selected.includes(label) ? "text-teal-400" : "text-white"}`}>{label}</span>
          </button>
        ))}
      </div>
      <div className="mt-2 text-sm text-teal-400 font-medium h-5">{selected.length > 0 ? `${selected.length} style${selected.length > 1 ? 's' : ''} selected` : null}</div>
      <section className="mt-12 flex flex-col md:flex-row gap-8 w-full items-center md:items-stretch">
        <div className="bg-gray-900 border-2 border-gray-800 rounded-2xl shadow-lg max-w-lg w-full p-8 relative flex flex-col items-center">
          <label htmlFor="room-upload-demo" className="w-full cursor-pointer flex flex-col items-center justify-center border-2 border-dashed border-teal-400/40 rounded-xl h-52 bg-gray-950/80 hover:border-teal-500 transition-all relative group">
            {preview ? (
              <img src={preview} alt="Preview" className="object-cover w-full h-full rounded-xl" />
            ) : (
              <>
                <svg className="w-12 h-12 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16V4m0 0A4 4 0 0115 4M7 4a4 4 0 018 0m0 0v12m6 4H5a2 2 0 01-2-2V8a2 2 0 012-2h4l2-2 2 2h4a2 2 0 012 2v10a2 2 0 01-2 2z" /></svg>
                <span className="text-gray-300 text-base font-medium">Upload a photo of your room</span>
                <span className="text-gray-500 text-xs mt-2 px-5 text-center">We'll redesign it with AI.<br />Take a picture in daylight. Hide nonessential items. Use the regular 1x lens.</span>
              </>
            )}
            <input id="room-upload-demo" type="file" accept="image/*" className="hidden" onChange={onFileChange} />
          </label>
          <button className="mt-5 mb-2 w-full px-5 py-3 rounded-lg bg-teal-600 font-semibold text-white text-lg border border-teal-400 hover:bg-teal-500 hover:border-teal-300 transition disabled:opacity-60" disabled={!file || selected.length === 0}>Redesign with AI</button>
        </div>
      </section>
    </main>
  );
}
