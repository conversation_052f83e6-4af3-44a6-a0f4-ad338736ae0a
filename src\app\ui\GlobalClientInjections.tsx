"use client";
import React from "react";

const POLYSANS_FONT_URLS = [
  { url: "https://ext.same-assets.com/4123950039/3713092101.ttf", weight: "400", style: "normal" },
  { url: "https://ext.same-assets.com/4123950039/1287537253.ttf", weight: "500", style: "normal" },
  { url: "https://ext.same-assets.com/4123950039/523861748.ttf", weight: "700", style: "normal" }
];

export default function GlobalClientInjections() {
  React.useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = POLYSANS_FONT_URLS.map(
      (f) => `\n@font-face {\n  font-family: 'PolySans';\n  src: url('${f.url}') format('truetype');\n  font-weight: ${f.weight};\n  font-style: ${f.style};\n  font-display: swap;\n}`
    ).join("\n");
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  React.useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      html {
        font-family: 'PolySans', system-ui, sans-serif;
        background: #0a0a0f;
        color: #fff;
        font-size: 16px;
        letter-spacing: -0.01em;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      body {
        background: #0a0a0f;
        color: #fff;
        margin: 0;
        min-height: 100vh;
      }
      ::selection {
        background: #e0e7ff;
        color: #18181b;
      }
      a {
        color: #6366f1;
        text-decoration: none;
        transition: color 0.15s;
      }
      a:hover {
        color: #4338ca;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);
  return null;
}
