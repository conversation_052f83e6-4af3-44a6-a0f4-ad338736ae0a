"use client";
import React from 'react';

interface ColorPalette {
  id: string;
  name: string;
  description: string;
  colors: string[];
  category: 'neutral' | 'warm' | 'cool' | 'bold' | 'natural';
}

interface ColorPaletteSelectorProps {
  selectedPalettes?: string[];
  onPaletteToggle: (paletteId: string) => void;
  onCustomPalette?: (colors: string[]) => void;
  maxSelection?: number;
  className?: string;
}

const COLOR_PALETTES: ColorPalette[] = [
  // Neutral Palettes
  {
    id: 'soft-neutrals',
    name: 'Soft Neutrals',
    description: 'Clean lines, neutral colors, uncluttered spaces',
    colors: ['#F5F5F5', '#E8E8E8', '#D3D3D3', '#C0C0C0', '#A9A9A9'],
    category: 'neutral'
  },
  {
    id: 'modern-grays',
    name: 'Modern Grays',
    description: 'Contemporary charcoal, silver, and white tones',
    colors: ['#2F2F2F', '#696969', '#A9A9A9', '#D3D3D3', '#F8F8FF'],
    category: 'neutral'
  },
  {
    id: 'warm-whites',
    name: 'Warm Whites',
    description: 'Ivory, cream, and off-white variations',
    colors: ['#FFFDD0', '#FDF5E6', '#FAF0E6', '#F5F5DC', '#FFF8DC'],
    category: 'neutral'
  },
  {
    id: 'nordic-lights',
    name: 'Nordic Lights',
    description: 'Scandinavian inspired whites and light grays',
    colors: ['#FFFFFF', '#F0F0F0', '#E0E0E0', '#D0D0D0', '#C0C0C0'],
    category: 'neutral'
  },
  {
    id: 'monochrome-classic',
    name: 'Monochrome Classic',
    description: 'Timeless black and white with gray transitions',
    colors: ['#000000', '#404040', '#808080', '#C0C0C0', '#FFFFFF'],
    category: 'neutral'
  },
  {
    id: 'sage-stone',
    name: 'Sage & Stone',
    description: 'Natural sage greens with stone neutrals',
    colors: ['#9CAF88', '#B8C5A6', '#D4DBC4', '#F0F1E2', '#FFFFFF'],
    category: 'neutral'
  },
  {
    id: 'industrial-gray',
    name: 'Industrial Gray',
    description: 'Urban industrial grays and charcoals',
    colors: ['#2F4F4F', '#696969', '#808080', '#A9A9A9', '#D3D3D3'],
    category: 'neutral'
  },
  {
    id: 'vintage-rose',
    name: 'Vintage Rose',
    description: 'Soft vintage rose with cream accents',
    colors: ['#B76E79', '#D4A5A5', '#E8C5C5', '#F2E5E5', '#FFFFFF'],
    category: 'neutral'
  },

  // Warm Palettes
  {
    id: 'warm-earth',
    name: 'Warm Earth',
    description: 'Rich browns, terracotta, and golden tones',
    colors: ['#8B4513', '#CD853F', '#DEB887', '#F4A460', '#FFE4B5'],
    category: 'warm'
  },
  {
    id: 'sunset-glow',
    name: 'Sunset Glow',
    description: 'Vibrant oranges, corals, and warm pinks',
    colors: ['#FF4500', '#FF6347', '#FF7F50', '#FFA07A', '#FFB6C1'],
    category: 'warm'
  },
  {
    id: 'autumn-harvest',
    name: 'Autumn Harvest',
    description: 'Deep burgundy, rust, and golden yellow',
    colors: ['#8B4513', '#A0522D', '#CD853F', '#DEB887', '#F5DEB3'],
    category: 'warm'
  },
  {
    id: 'terracotta-warmth',
    name: 'Terracotta Warmth',
    description: 'Mediterranean terracotta with warm neutrals',
    colors: ['#A0522D', '#CD853F', '#DEB887', '#F5DEB3', '#FFF8DC'],
    category: 'warm'
  },
  {
    id: 'golden-hour',
    name: 'Golden Hour',
    description: 'Warm golds and honey tones',
    colors: ['#B8860B', '#DAA520', '#FFD700', '#FFFF00', '#FFFACD'],
    category: 'warm'
  },
  {
    id: 'blush-gold',
    name: 'Blush & Gold',
    description: 'Soft blush pinks with golden accents',
    colors: ['#FFB6C1', '#FFC0CB', '#FFD700', '#FFFFE0', '#FFFACD'],
    category: 'warm'
  },
  {
    id: 'rose-garden',
    name: 'Rose Garden',
    description: 'Romantic roses from deep to light',
    colors: ['#8B0000', '#DC143C', '#FF1493', '#FF69B4', '#FFB6C1'],
    category: 'warm'
  },
  {
    id: 'desert-sand',
    name: 'Desert Sand',
    description: 'Warm desert sands and earth tones',
    colors: ['#8B7355', '#D2B48C', '#F4A460', '#FAD5A5', '#FFEFD5'],
    category: 'warm'
  },
  {
    id: 'chocolate-brown',
    name: 'Chocolate Brown',
    description: 'Rich chocolate browns and warm tans',
    colors: ['#3C1810', '#654321', '#8B4513', '#A0522D', '#D2B48C'],
    category: 'warm'
  },
  {
    id: 'citrus-burst',
    name: 'Citrus Burst',
    description: 'Energetic oranges and sunny yellows',
    colors: ['#FF4500', '#FF6347', '#FFA500', '#FFD700', '#FFFF00'],
    category: 'warm'
  },
  {
    id: 'coral-reef',
    name: 'Coral Reef',
    description: 'Tropical coral and peach tones',
    colors: ['#FF5722', '#FF7043', '#FF8A65', '#FFAB91', '#FFCCBC'],
    category: 'warm'
  },

  // Cool Palettes
  {
    id: 'coastal-calm',
    name: 'Coastal Calm',
    description: 'Ocean blues with light coastal tones',
    colors: ['#E6F3FF', '#B3D9FF', '#80BFFF', '#4DA6FF', '#1A8CFF'],
    category: 'cool'
  },
  {
    id: 'ocean-breeze',
    name: 'Ocean Breeze',
    description: 'Deep ocean blues to light sky',
    colors: ['#006994', '#0085C3', '#00A1C9', '#00BFFF', '#87CEEB'],
    category: 'cool'
  },
  {
    id: 'forest-retreat',
    name: 'Forest Retreat',
    description: 'Deep forest greens to light sage',
    colors: ['#2D5016', '#4A7C59', '#68A357', '#86C56A', '#A4E87D'],
    category: 'cool'
  },
  {
    id: 'deep-ocean',
    name: 'Deep Ocean',
    description: 'Deep navy to bright ocean blues',
    colors: ['#003366', '#004080', '#0066CC', '#3399FF', '#66B2FF'],
    category: 'cool'
  },
  {
    id: 'emerald-forest',
    name: 'Emerald Forest',
    description: 'Rich emerald greens and forest tones',
    colors: ['#013220', '#355E3B', '#50C878', '#90EE90', '#F0FFF0'],
    category: 'cool'
  },
  {
    id: 'midnight-blue',
    name: 'Midnight Blue',
    description: 'Deep midnight to bright sky blues',
    colors: ['#191970', '#000080', '#0000CD', '#4169E1', '#6495ED'],
    category: 'cool'
  },
  {
    id: 'arctic-ice',
    name: 'Arctic Ice',
    description: 'Cool arctic blues and icy whites',
    colors: ['#B0E0E6', '#E0FFFF', '#F0F8FF', '#F8F8FF', '#FFFFFF'],
    category: 'cool'
  },
  {
    id: 'tropical-paradise',
    name: 'Tropical Paradise',
    description: 'Lush tropical greens and fresh tones',
    colors: ['#228B22', '#32CD32', '#7CFC00', '#ADFF2F', '#F0FFF0'],
    category: 'cool'
  },
  {
    id: 'fresh-mint',
    name: 'Fresh Mint',
    description: 'Cool mint greens and fresh whites',
    colors: ['#00FF7F', '#98FB98', '#90EE90', '#F0FFF0', '#FFFFFF'],
    category: 'cool'
  },
  {
    id: 'peacock-blue',
    name: 'Peacock Blue',
    description: 'Rich peacock blues and teals',
    colors: ['#005F69', '#008B8B', '#20B2AA', '#48D1CC', '#AFEEEE'],
    category: 'cool'
  },
  {
    id: 'stormy-sky',
    name: 'Stormy Sky',
    description: 'Dramatic storm grays and blues',
    colors: ['#2F4F4F', '#708090', '#778899', '#B0C4DE', '#E6E6FA'],
    category: 'cool'
  },

  // Bold Palettes
  {
    id: 'lavender-dreams',
    name: 'Lavender Dreams',
    description: 'Rich purples and lavender tones',
    colors: ['#663399', '#8A2BE2', '#9370DB', '#BA55D3', '#DDA0DD'],
    category: 'bold'
  },
  {
    id: 'royal-purple',
    name: 'Royal Purple',
    description: 'Majestic purples from deep to light',
    colors: ['#4B0082', '#663399', '#8A2BE2', '#9370DB', '#DDA0DD'],
    category: 'bold'
  },
  {
    id: 'jewel-tones',
    name: 'Jewel Tones',
    description: 'Rich emerald, sapphire, and amethyst',
    colors: ['#50C878', '#0F52BA', '#9966CC', '#FFD700', '#2F2F2F'],
    category: 'bold'
  },
  {
    id: 'vibrant-energy',
    name: 'Vibrant Energy',
    description: 'Electric blues, bright corals, and sunny yellows',
    colors: ['#00BFFF', '#FF6347', '#FFD700', '#32CD32', '#FF1493'],
    category: 'bold'
  },
  {
    id: 'dramatic-contrast',
    name: 'Dramatic Contrast',
    description: 'Bold black, white, and accent colors',
    colors: ['#000000', '#FFFFFF', '#FF0000', '#FFD700', '#4169E1'],
    category: 'bold'
  },

  // Natural Palettes
  {
    id: 'botanical-garden',
    name: 'Botanical Garden',
    description: 'Fresh greens, earth tones, and floral accents',
    colors: ['#228B22', '#8FBC8F', '#DEB887', '#F5DEB3', '#FFB6C1'],
    category: 'natural'
  },
  {
    id: 'desert-sunset',
    name: 'Desert Sunset',
    description: 'Warm sands, cactus greens, and sunset pinks',
    colors: ['#F4A460', '#CD853F', '#9ACD32', '#FF69B4', '#FFE4E1'],
    category: 'natural'
  },
  {
    id: 'mountain-mist',
    name: 'Mountain Mist',
    description: 'Stone grays, misty blues, and alpine greens',
    colors: ['#708090', '#B0C4DE', '#2E8B57', '#F5F5DC', '#FFFAFA'],
    category: 'natural'
  },
  {
    id: 'earth-tones',
    name: 'Earth Tones',
    description: 'Natural earth colors and organic browns',
    colors: ['#8B4513', '#A0522D', '#CD853F', '#DEB887', '#F5DEB3'],
    category: 'natural'
  },
  {
    id: 'forest-floor',
    name: 'Forest Floor',
    description: 'Deep forest greens and natural browns',
    colors: ['#2D5016', '#4A7C59', '#68A357', '#8B4513', '#DEB887'],
    category: 'natural'
  },
  {
    id: 'ocean-depths',
    name: 'Ocean Depths',
    description: 'Deep ocean blues and sea greens',
    colors: ['#003366', '#006994', '#008B8B', '#20B2AA', '#87CEEB'],
    category: 'natural'
  },
  {
    id: 'sunrise-meadow',
    name: 'Sunrise Meadow',
    description: 'Soft meadow greens with sunrise golds',
    colors: ['#9ACD32', '#90EE90', '#FFD700', '#FFA500', '#FFFFE0'],
    category: 'natural'
  },
  {
    id: 'autumn-leaves',
    name: 'Autumn Leaves',
    description: 'Rich autumn foliage colors',
    colors: ['#8B4513', '#CD853F', '#DAA520', '#FF8C00', '#FFD700'],
    category: 'natural'
  },
  {
    id: 'stone-garden',
    name: 'Stone Garden',
    description: 'Natural stone grays and earth tones',
    colors: ['#696969', '#808080', '#A9A9A9', '#D3D3D3', '#F5F5DC'],
    category: 'natural'
  },
  {
    id: 'wildflower-field',
    name: 'Wildflower Field',
    description: 'Colorful wildflower meadow palette',
    colors: ['#9370DB', '#FF69B4', '#FFD700', '#32CD32', '#87CEEB'],
    category: 'natural'
  },

  // Additional Palettes to reach 50+
  {
    id: 'burgundy-wine',
    name: 'Burgundy Wine',
    description: 'Rich burgundy and wine tones',
    colors: ['#800020', '#A0002A', '#C41E3A', '#DC143C', '#F08080'],
    category: 'bold'
  },
  {
    id: 'copper-bronze',
    name: 'Copper Bronze',
    description: 'Metallic copper and bronze shades',
    colors: ['#B87333', '#CD7F32', '#D2691E', '#DAA520', '#F4A460'],
    category: 'warm'
  },
  {
    id: 'arctic-aurora',
    name: 'Arctic Aurora',
    description: 'Northern lights inspired palette',
    colors: ['#00FF7F', '#00CED1', '#4169E1', '#9370DB', '#FF1493'],
    category: 'bold'
  },
  {
    id: 'mushroom-gray',
    name: 'Mushroom Gray',
    description: 'Soft mushroom and taupe grays',
    colors: ['#8B7D6B', '#A0937D', '#B8A99A', '#D2B48C', '#F5F5DC'],
    category: 'neutral'
  },
  {
    id: 'jade-green',
    name: 'Jade Green',
    description: 'Precious jade green tones',
    colors: ['#00A86B', '#20B2AA', '#3CB371', '#90EE90', '#F0FFF0'],
    category: 'cool'
  },
  {
    id: 'sunset-pink',
    name: 'Sunset Pink',
    description: 'Romantic sunset pink hues',
    colors: ['#FF1493', '#FF69B4', '#FFB6C1', '#FFC0CB', '#FFCCCB'],
    category: 'warm'
  },
  {
    id: 'steel-blue',
    name: 'Steel Blue',
    description: 'Industrial steel blue tones',
    colors: ['#4682B4', '#5F9EA0', '#6495ED', '#87CEEB', '#B0E0E6'],
    category: 'cool'
  },
  {
    id: 'amber-honey',
    name: 'Amber Honey',
    description: 'Golden amber and honey colors',
    colors: ['#FFBF00', '#FFD700', '#FFDB58', '#F0E68C', '#FFFACD'],
    category: 'warm'
  }
];

const CATEGORY_LABELS = {
  neutral: 'Neutral',
  warm: 'Warm',
  cool: 'Cool',
  bold: 'Bold',
  natural: 'Natural',
  custom: 'Custom'
};

export const ColorPaletteSelector: React.FC<ColorPaletteSelectorProps> = ({
  selectedPalettes = [],
  onPaletteToggle,
  onCustomPalette,
  maxSelection = 10,
  className = ""
}) => {
  const [selectedCategory, setSelectedCategory] = React.useState<string>('all');
  const [showCustomPicker, setShowCustomPicker] = React.useState<boolean>(false);
  const [customColors, setCustomColors] = React.useState<string[]>(['#FFFFFF', '#F0F0F0', '#E0E0E0', '#D0D0D0', '#C0C0C0']);
  const [customPalettes, setCustomPalettes] = React.useState<ColorPalette[]>([]);
  const [customPaletteCounter, setCustomPaletteCounter] = React.useState<number>(1);
  const [isMounted, setIsMounted] = React.useState<boolean>(false);

  // Prevent hydration mismatch by only rendering after mount
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  // Combine regular palettes with custom palettes
  const allPalettes = [...COLOR_PALETTES, ...customPalettes];

  const filteredPalettes = selectedCategory === 'all'
    ? allPalettes
    : selectedCategory === 'custom'
      ? allPalettes.filter(palette => palette.id.startsWith('custom-'))
      : allPalettes.filter(palette => palette.category === selectedCategory);

  const isSelected = (paletteId: string) => selectedPalettes.includes(paletteId);
  const canSelect = selectedPalettes.length < maxSelection;

  const handlePaletteClick = (paletteId: string) => {
    if (isSelected(paletteId) || canSelect) {
      onPaletteToggle(paletteId);
    }
  };

  // Prevent hydration mismatch
  if (!isMounted) {
    return (
      <div className={`w-full ${className}`}>
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-white mb-2">Choose Color Palettes</h3>
          <p className="text-gray-400 text-sm">
            Loading color palettes...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Color Palettes</h3>
        <p className="text-gray-400 text-sm">
          Select multiple color schemes for your production design ({selectedPalettes.length}/{maxSelection} selected)
        </p>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
              selectedCategory === 'all'
                ? 'bg-teal-600 text-white border-teal-400'
                : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
            }`}
          >
            All
          </button>
          {Object.entries(CATEGORY_LABELS).map(([key, label]) => {
            // Only show custom category if there are custom palettes
            if (key === 'custom' && customPalettes.length === 0) return null;

            return (
              <button
                key={key}
                onClick={() => setSelectedCategory(key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
                  selectedCategory === key
                    ? 'bg-teal-600 text-white border-teal-400'
                    : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
                }`}
              >
                {label} {key === 'custom' && `(${customPalettes.length})`}
              </button>
            );
          })}
        </div>
      </div>

      {/* Palettes Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredPalettes.map((palette) => {
          const selected = isSelected(palette.id);
          const disabled = !selected && !canSelect;

          return (
            <button
              key={palette.id}
              onClick={() => handlePaletteClick(palette.id)}
              disabled={disabled}
              className={`
                group flex flex-col p-4 rounded-xl border-2 transition-all duration-200 relative text-left
                ${selected
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40'
                  : disabled
                    ? 'border-gray-700 bg-gray-900/50 opacity-50 cursor-not-allowed'
                    : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800 active:scale-95'
                }
              `}
            >
              {/* Color Swatches */}
              <div className="flex gap-1 mb-3">
                {palette.colors.map((color, index) => (
                  <div
                    key={index}
                    className="w-8 h-8 rounded-lg border border-gray-600 flex-1"
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>

              {/* Palette Info */}
              <div>
                <h4 className={`font-medium mb-1 ${
                  selected ? 'text-teal-400' : 'text-white'
                }`}>
                  {palette.name}
                </h4>
                <p className="text-gray-400 text-xs leading-relaxed">
                  {palette.description}
                </p>
              </div>

              {/* Category Badge */}
              <div className="mt-3">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  selected
                    ? 'bg-teal-500/20 text-teal-300'
                    : 'bg-gray-700 text-gray-400'
                }`}>
                  {CATEGORY_LABELS[palette.category]}
                </span>
              </div>

              {/* Selection Indicator */}
              {selected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}

        {/* Custom Color Palette Option */}
        <div className="col-span-full">
          <button
            onClick={() => setShowCustomPicker(!showCustomPicker)}
            className="w-full p-4 rounded-xl border-2 border-dashed border-gray-600 hover:border-teal-500 transition-all duration-200 bg-gray-900/50 hover:bg-gray-800/50"
          >
            <div className="flex items-center justify-center gap-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-red-500 via-green-500 to-blue-500"></div>
              <span className="text-white font-medium">Create Custom Palette</span>
              <svg className={`w-5 h-5 text-gray-400 transition-transform ${showCustomPicker ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </button>
        </div>
      </div>

      {/* Custom Color Picker */}
      {showCustomPicker && (
        <div className="mt-6 p-6 bg-gray-800/30 rounded-xl border border-gray-700">
          <h4 className="text-white font-medium mb-4">🎨 Create Your Custom Palette</h4>
          <p className="text-gray-400 text-sm mb-4">
            Choose 5 colors to create your personalized color palette. Click on each color to change it.
          </p>

          <div className="grid grid-cols-5 gap-3 mb-4">
            {customColors.map((color, index) => (
              <div key={index} className="flex flex-col items-center">
                <input
                  type="color"
                  value={color}
                  onChange={(e) => {
                    const newColors = [...customColors];
                    newColors[index] = e.target.value;
                    setCustomColors(newColors);
                  }}
                  className="w-16 h-16 rounded-lg border-2 border-gray-600 cursor-pointer hover:border-teal-500 transition-colors"
                  title={`Color ${index + 1}: ${color}`}
                />
                <span className="text-xs text-gray-400 mt-1 font-mono">{color}</span>
              </div>
            ))}
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => {
                // Create a unique ID for the custom palette using counter
                const customId = `custom-${customPaletteCounter}`;

                // Create the custom palette object
                const newCustomPalette: ColorPalette = {
                  id: customId,
                  name: `Custom Palette ${customPaletteCounter}`,
                  description: 'Your custom color selection',
                  colors: [...customColors],
                  category: 'neutral'
                };

                // Add to custom palettes and increment counter
                setCustomPalettes(prev => [...prev, newCustomPalette]);
                setCustomPaletteCounter(prev => prev + 1);

                // Call the callback if provided
                if (onCustomPalette) {
                  onCustomPalette(customColors);
                }

                // Select the new custom palette
                onPaletteToggle(customId);
                setShowCustomPicker(false);
              }}
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg font-medium transition-colors"
            >
              Apply Custom Palette
            </button>
            <button
              onClick={() => setCustomColors(['#FFFFFF', '#F0F0F0', '#E0E0E0', '#D0D0D0', '#C0C0C0'])}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium transition-colors"
            >
              Reset Colors
            </button>
          </div>
        </div>
      )}

      {/* Selected Palettes Info */}
      {selectedPalettes.length > 0 && (
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700">
          <h4 className="text-white font-medium mb-3">Selected Palettes ({selectedPalettes.length})</h4>
          <div className="space-y-3">
            {selectedPalettes.map(paletteId => {
              const palette = allPalettes.find(p => p.id === paletteId);
              if (!palette) return null;

              return (
                <div key={paletteId} className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="flex gap-1">
                      {palette.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-4 h-4 rounded border border-gray-600"
                          style={{ backgroundColor: color }}
                          title={color}
                        />
                      ))}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-white text-sm font-medium">{palette.name}</span>
                      {palette.id.startsWith('custom-') && (
                        <span className="text-xs bg-teal-600 text-white px-2 py-0.5 rounded-full">Custom</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {palette.id.startsWith('custom-') && (
                      <button
                        onClick={() => {
                          setCustomColors(palette.colors);
                          setShowCustomPicker(true);
                          onPaletteToggle(paletteId); // Remove current custom palette
                          setCustomPalettes(prev => prev.filter(p => p.id !== paletteId)); // Remove from custom palettes
                        }}
                        className="text-gray-400 hover:text-blue-400 transition-colors"
                        title="Edit custom palette"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    )}
                    <button
                      onClick={() => {
                        onPaletteToggle(paletteId);
                        if (palette.id.startsWith('custom-')) {
                          setCustomPalettes(prev => prev.filter(p => p.id !== paletteId));
                        }
                      }}
                      className="text-gray-400 hover:text-red-400 transition-colors"
                      title="Remove palette"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ColorPaletteSelector;
export { COLOR_PALETTES, CATEGORY_LABELS };
export type { ColorPalette };
