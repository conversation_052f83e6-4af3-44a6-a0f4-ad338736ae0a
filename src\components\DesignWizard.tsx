"use client";
import React, { useState } from 'react';
import RoomTypeSelector from './RoomTypeSelector';
import StyleSelector from './StyleSelector';
import ColorPaletteSelector from './ColorPaletteSelector';
import MaterialSelector from './MaterialSelector';
import ImageUploader from './ImageUploader';
import { SAMPLE_MATERIALS } from './MaterialSelector';

interface DesignConfig {
  mode: 'restyle' | 'generate';
  roomType: string | null;
  styles: number[];
  colorPalettes: string[];
  materials: number[];
  image: File | null;
}

interface DesignWizardProps {
  onComplete: (config: DesignConfig) => void;
  className?: string;
}

type WizardStep = 'mode' | 'room-type' | 'styles' | 'colors' | 'materials' | 'image' | 'review';

const STEP_TITLES = {
  'mode': 'Choose Design Mode',
  'room-type': 'Select Room Type',
  'styles': 'Pick Design Styles',
  'colors': 'Choose Color Palettes',
  'materials': 'Select Materials',
  'image': 'Upload Room Photo',
  'review': 'Review & Generate'
};

const STEP_DESCRIPTIONS = {
  'mode': 'How would you like to create your design?',
  'room-type': 'What type of room are you designing?',
  'styles': 'What design styles inspire you?',
  'colors': 'What color schemes do you prefer?',
  'materials': 'What materials would you like to feature?',
  'image': 'Upload a photo of your current room',
  'review': 'Review your selections and generate your design'
};

export const DesignWizard: React.FC<DesignWizardProps> = ({
  onComplete,
  className = ""
}) => {
  const [currentStep, setCurrentStep] = useState<WizardStep>('mode');
  const [config, setConfig] = useState<DesignConfig>({
    mode: 'restyle',
    roomType: null,
    styles: [],
    colorPalettes: [],
    materials: [],
    image: null
  });

  const updateConfig = (updates: Partial<DesignConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const getStepOrder = (): WizardStep[] => {
    if (config.mode === 'restyle') {
      return ['mode', 'room-type', 'styles', 'colors', 'materials', 'image', 'review'];
    } else {
      return ['mode', 'room-type', 'styles', 'colors', 'materials', 'review'];
    }
  };

  const stepOrder = getStepOrder();
  const currentStepIndex = stepOrder.indexOf(currentStep);
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === stepOrder.length - 1;

  const canProceed = (): boolean => {
    switch (currentStep) {
      case 'mode': return true; // Mode is always selected
      case 'room-type': return config.roomType !== null;
      case 'styles': return config.styles.length > 0;
      case 'colors': return config.colorPalettes.length > 0;
      case 'materials': return config.materials.length > 0;
      case 'image': return config.mode === 'generate' || config.image !== null;
      case 'review': return true;
      default: return false;
    }
  };

  const handleNext = () => {
    if (isLastStep) {
      onComplete(config);
    } else {
      const nextIndex = currentStepIndex + 1;
      setCurrentStep(stepOrder[nextIndex]);
    }
  };

  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevIndex = currentStepIndex - 1;
      setCurrentStep(stepOrder[prevIndex]);
    }
  };

  const handleStepClick = (step: WizardStep) => {
    const stepIndex = stepOrder.indexOf(step);
    if (stepIndex <= currentStepIndex) {
      setCurrentStep(step);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'mode':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <button
                onClick={() => updateConfig({ mode: 'restyle' })}
                className={`p-6 rounded-xl border-2 transition-all text-left ${
                  config.mode === 'restyle'
                    ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40'
                    : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800'
                }`}
              >
                <div className="text-4xl mb-3">🔄</div>
                <h3 className="text-xl font-semibold text-white mb-2">Restyle Existing Room</h3>
                <p className="text-gray-400 text-sm">
                  Upload a photo of your current room and transform it with AI
                </p>
              </button>

              <button
                onClick={() => updateConfig({ mode: 'generate' })}
                className={`p-6 rounded-xl border-2 transition-all text-left ${
                  config.mode === 'generate'
                    ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40'
                    : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800'
                }`}
              >
                <div className="text-4xl mb-3">✨</div>
                <h3 className="text-xl font-semibold text-white mb-2">Generate New Design</h3>
                <p className="text-gray-400 text-sm">
                  Create a completely new room design from scratch
                </p>
              </button>
            </div>
          </div>
        );

      case 'room-type':
        return (
          <RoomTypeSelector
            selectedRoomType={config.roomType}
            onRoomTypeSelect={(roomType) => updateConfig({ roomType })}
          />
        );

      case 'styles':
        return (
          <StyleSelector
            selectedStyles={config.styles}
            onStyleToggle={(styleId) => {
              const newStyles = config.styles.includes(styleId)
                ? config.styles.filter(id => id !== styleId)
                : [...config.styles, styleId];
              updateConfig({ styles: newStyles });
            }}
            maxSelection={5}
          />
        );

      case 'colors':
        return (
          <ColorPaletteSelector
            selectedPalettes={config.colorPalettes}
            onPaletteToggle={(paletteId) => {
              const newPalettes = config.colorPalettes.includes(paletteId)
                ? config.colorPalettes.filter(id => id !== paletteId)
                : [...config.colorPalettes, paletteId];
              updateConfig({ colorPalettes: newPalettes });
            }}
            maxSelection={5}
          />
        );

      case 'materials':
        return (
          <MaterialSelector
            materials={SAMPLE_MATERIALS}
            selectedMaterials={config.materials}
            onMaterialToggle={(materialId) => {
              const newMaterials = config.materials.includes(materialId)
                ? config.materials.filter(id => id !== materialId)
                : [...config.materials, materialId];
              updateConfig({ materials: newMaterials });
            }}
            maxSelection={5}
          />
        );

      case 'image':
        return (
          <ImageUploader
            selectedImage={config.image}
            onImageSelect={(image) => updateConfig({ image })}
          />
        );

      case 'review':
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-semibold text-white mb-2">Ready to Generate!</h3>
              <p className="text-gray-400">
                Review your selections below and click Generate to create your design
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Configuration Summary */}
              <div className="space-y-4">
                <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                  <h4 className="text-white font-medium mb-2">Design Mode</h4>
                  <p className="text-gray-400 text-sm capitalize">{config.mode}</p>
                </div>

                <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                  <h4 className="text-white font-medium mb-2">Room Type</h4>
                  <p className="text-gray-400 text-sm">{config.roomType || 'Not selected'}</p>
                </div>

                <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                  <h4 className="text-white font-medium mb-2">Styles ({config.styles.length})</h4>
                  <p className="text-gray-400 text-sm">
                    {config.styles.length > 0 ? `${config.styles.length} styles selected` : 'No styles selected'}
                  </p>
                </div>

                <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                  <h4 className="text-white font-medium mb-2">Color Palettes ({config.colorPalettes.length})</h4>
                  <p className="text-gray-400 text-sm">
                    {config.colorPalettes.length > 0
                      ? config.colorPalettes.join(', ')
                      : 'Not selected'
                    }
                  </p>
                </div>

                <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                  <h4 className="text-white font-medium mb-2">Materials ({config.materials.length})</h4>
                  <p className="text-gray-400 text-sm">
                    {config.materials.length > 0 ? `${config.materials.length} materials selected` : 'No materials selected'}
                  </p>
                </div>
              </div>

              {/* Image Preview */}
              {config.mode === 'restyle' && config.image && (
                <div className="bg-gray-800/30 rounded-xl p-4 border border-gray-700">
                  <h4 className="text-white font-medium mb-3">Room Photo</h4>
                  <img
                    src={URL.createObjectURL(config.image)}
                    alt="Room preview"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                </div>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`w-full max-w-6xl mx-auto ${className}`}>
      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white">{STEP_TITLES[currentStep]}</h2>
          <span className="text-sm text-gray-400">
            Step {currentStepIndex + 1} of {stepOrder.length}
          </span>
        </div>
        <p className="text-gray-400 mb-4">{STEP_DESCRIPTIONS[currentStep]}</p>
        
        <div className="w-full bg-gray-800 rounded-full h-2">
          <div
            className="bg-teal-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStepIndex + 1) / stepOrder.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Step Navigation */}
      <div className="flex flex-wrap gap-2 mb-8">
        {stepOrder.map((step, index) => (
          <button
            key={step}
            onClick={() => handleStepClick(step)}
            disabled={index > currentStepIndex}
            className={`px-3 py-1 rounded-lg text-sm font-medium transition-all ${
              step === currentStep
                ? 'bg-teal-600 text-white'
                : index < currentStepIndex
                ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                : 'bg-gray-800 text-gray-500 cursor-not-allowed'
            }`}
          >
            {index + 1}. {STEP_TITLES[step]}
          </button>
        ))}
      </div>

      {/* Step Content */}
      <div className="mb-8">
        {renderStepContent()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <button
          onClick={handlePrevious}
          disabled={isFirstStep}
          className={`px-6 py-3 rounded-lg font-medium transition-all ${
            isFirstStep
              ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
              : 'bg-gray-700 text-white hover:bg-gray-600'
          }`}
        >
          Previous
        </button>

        <button
          onClick={handleNext}
          disabled={!canProceed()}
          className={`px-6 py-3 rounded-lg font-medium transition-all ${
            !canProceed()
              ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
              : isLastStep
              ? 'bg-teal-600 text-white hover:bg-teal-500'
              : 'bg-teal-600 text-white hover:bg-teal-500'
          }`}
        >
          {isLastStep ? 'Generate Design' : 'Next'}
        </button>
      </div>
    </div>
  );
};

export default DesignWizard;

