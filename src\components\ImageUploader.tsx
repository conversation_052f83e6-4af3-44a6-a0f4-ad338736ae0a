"use client";
import React, { useState, useCallback } from 'react';

interface ImageUploaderProps {
  onImageSelect: (file: File | null) => void;
  selectedImage: File | null;
  className?: string;
  maxSizeInMB?: number;
  acceptedFormats?: string[];
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageSelect,
  selectedImage,
  className = "",
  maxSizeInMB = 10,
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Generate preview URL when image changes
  React.useEffect(() => {
    if (selectedImage) {
      const url = URL.createObjectURL(selectedImage);
      setPreview(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setPreview(null);
    }
  }, [selectedImage]);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!acceptedFormats.includes(file.type)) {
      return `Please upload a valid image file (${acceptedFormats.map(f => f.split('/')[1]).join(', ')})`;
    }

    // Check file size
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      return `File size must be less than ${maxSizeInMB}MB`;
    }

    return null;
  };

  const handleFileSelect = useCallback(async (file: File) => {
    setError(null);
    setIsProcessing(true);

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      setIsProcessing(false);
      return;
    }

    try {
      // Additional validation: check if it's actually an image
      const img = new Image();
      img.onload = () => {
        onImageSelect(file);
        setIsProcessing(false);
      };
      img.onerror = () => {
        setError('Invalid image file');
        setIsProcessing(false);
      };
      img.src = URL.createObjectURL(file);
    } catch (err) {
      setError('Error processing image');
      setIsProcessing(false);
    }
  }, [onImageSelect, maxSizeInMB, acceptedFormats]);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleRemoveImage = useCallback(() => {
    onImageSelect(null);
    setError(null);
  }, [onImageSelect]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-4">
        <h3 className="text-xl font-semibold text-white mb-2">Upload Room Photo</h3>
        <p className="text-gray-400 text-sm">
          Upload a clear photo of your room for AI redesign
        </p>
      </div>

      {!selectedImage ? (
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          className={`
            relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer
            ${isDragOver 
              ? 'border-teal-400 bg-teal-500/10' 
              : 'border-gray-600 hover:border-teal-500 hover:bg-gray-800/50'
            }
            ${isProcessing ? 'pointer-events-none opacity-50' : ''}
          `}
        >
          <input
            type="file"
            accept={acceptedFormats.join(',')}
            onChange={handleFileInputChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={isProcessing}
          />

          <div className="flex flex-col items-center gap-4">
            {isProcessing ? (
              <div className="flex flex-col items-center gap-3">
                <div className="w-12 h-12 border-4 border-teal-500 border-t-transparent rounded-full animate-spin"></div>
                <p className="text-gray-300">Processing image...</p>
              </div>
            ) : (
              <>
                <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                </div>
                <div>
                  <p className="text-lg font-medium text-white mb-1">
                    Drop your image here, or <span className="text-teal-400">browse</span>
                  </p>
                  <p className="text-sm text-gray-400">
                    Supports: {acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')} • Max {maxSizeInMB}MB
                  </p>
                </div>
              </>
            )}
          </div>

          {/* Tips */}
          <div className="mt-6 p-4 bg-gray-800/50 rounded-lg">
            <h4 className="text-sm font-medium text-white mb-2">📸 Photo Tips:</h4>
            <ul className="text-xs text-gray-400 space-y-1 text-left">
              <li>• Take photos in good lighting (natural daylight works best)</li>
              <li>• Include the entire room or the area you want to redesign</li>
              <li>• Remove clutter and personal items for better results</li>
              <li>• Use the regular 1x camera lens (avoid wide-angle)</li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Image Preview */}
          <div className="relative bg-gray-900 rounded-xl border-2 border-gray-700 overflow-hidden">
            <img
              src={preview || ''}
              alt="Room preview"
              className="w-full h-64 sm:h-80 object-cover"
            />
            <button
              onClick={handleRemoveImage}
              className="absolute top-3 right-3 w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors"
              title="Remove image"
            >
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          {/* File Info */}
          <div className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg border border-gray-700">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-teal-500 rounded flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-white font-medium text-sm">{selectedImage.name}</p>
                <p className="text-gray-400 text-xs">{formatFileSize(selectedImage.size)}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-teal-400 bg-teal-500/20 px-2 py-1 rounded">Ready</span>
            </div>
          </div>

          {/* Replace Button */}
          <button
            onClick={() => document.querySelector<HTMLInputElement>('input[type="file"]')?.click()}
            className="w-full py-2 px-4 bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg text-white text-sm font-medium transition-colors"
          >
            Replace Image
          </button>
          <input
            type="file"
            accept={acceptedFormats.join(',')}
            onChange={handleFileInputChange}
            className="hidden"
          />
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mt-4 p-3 bg-red-500/10 border border-red-500/30 rounded-lg">
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="text-red-400 text-sm font-medium">{error}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
