"use client";
import React from 'react';
import { LicenseManager, type SubscriptionTier } from '../lib/license';

interface LicenseDisplayProps {
  tier: SubscriptionTier;
  className?: string;
  showDetails?: boolean;
}

export const LicenseDisplay: React.FC<LicenseDisplayProps> = ({
  tier,
  className = "",
  showDetails = true
}) => {
  const license = LicenseManager.getLicenseInfo(tier);
  
  const getLicenseColor = () => {
    switch (license.type) {
      case 'commercial':
      case 'enterprise':
        return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'personal':
        return 'text-blue-400 bg-blue-500/10 border-blue-500/30';
      default:
        return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getLicenseIcon = () => {
    if (license.commercialUse) {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      );
    }
  };

  return (
    <div className={`${className}`}>
      {/* License Badge */}
      <div className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${getLicenseColor()}`}>
        {getLicenseIcon()}
        <span className="font-medium text-sm">
          {license.commercialUse ? 'Commercial License' : 'Personal Use Only'}
        </span>
      </div>

      {/* Detailed Information */}
      {showDetails && (
        <div className="mt-4 space-y-4">
          {/* Features */}
          <div>
            <h4 className="text-white font-medium mb-2">Included Features:</h4>
            <ul className="space-y-1">
              {license.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2 text-sm text-gray-300">
                  <svg className="w-4 h-4 text-green-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  {feature}
                </li>
              ))}
            </ul>
          </div>

          {/* Restrictions */}
          {license.restrictions.length > 0 && (
            <div>
              <h4 className="text-white font-medium mb-2">Restrictions:</h4>
              <ul className="space-y-1">
                {license.restrictions.map((restriction, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-gray-400">
                    <svg className="w-4 h-4 text-yellow-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    {restriction}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Usage Limits */}
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h4 className="text-white font-medium mb-3">Usage Limits:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Images per month:</span>
                <span className="text-white font-medium ml-2">{license.maxImages}</span>
              </div>
              <div>
                <span className="text-gray-400">Max users:</span>
                <span className="text-white font-medium ml-2">{license.maxUsers}</span>
              </div>
              <div>
                <span className="text-gray-400">Resolution:</span>
                <span className="text-white font-medium ml-2 capitalize">{license.resolutionLevel}</span>
              </div>
              <div>
                <span className="text-gray-400">Watermark:</span>
                <span className={`font-medium ml-2 ${license.watermark ? 'text-yellow-400' : 'text-green-400'}`}>
                  {license.watermark ? 'Yes' : 'None'}
                </span>
              </div>
            </div>
          </div>

          {/* Commercial Use Notice */}
          {!license.commercialUse && (
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <svg className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h5 className="text-blue-400 font-medium mb-1">Need Commercial Use?</h5>
                  <p className="text-sm text-gray-300">
                    Upgrade to Pro or Team plan to use your designs for client work, marketing, and business purposes.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LicenseDisplay;
