"use client";
import React, { useState } from 'react';
import { getUniqueMaterialIcon } from './icons/UniqueMaterialIcons';

interface Material {
  id: number;
  name: string;
  category: string;
  preview_image?: string;
}

interface MaterialSelectorProps {
  materials: Material[];
  selectedMaterials: number[];
  onMaterialToggle: (materialId: number) => void;
  maxSelection?: number;
}

const MATERIAL_CATEGORIES = [
  'All',
  'Wood',
  'Metal',
  'Stone',
  'Fabric',
  'Glass',
  'Ceramic',
  'Synthetic',
  'Natural',
  'Composite',
  'Sculpture'
];

export const MaterialSelector: React.FC<MaterialSelectorProps> = ({
  materials,
  selectedMaterials,
  onMaterialToggle,
  maxSelection = 5
}) => {
  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredMaterials = selectedCategory === 'All' 
    ? materials 
    : materials.filter(material => material.category === selectedCategory);

  const isSelected = (materialId: number) => selectedMaterials.includes(materialId);
  const canSelect = selectedMaterials.length < maxSelection;

  const handleMaterialClick = (materialId: number) => {
    if (isSelected(materialId) || canSelect) {
      onMaterialToggle(materialId);
    }
  };

  return (
    <div className="w-full">
      {/* Category Filter */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-white mb-3">Material Categories</h3>
        <div className="flex flex-wrap gap-2">
          {MATERIAL_CATEGORIES.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                selectedCategory === category
                  ? 'bg-teal-600 text-white border-teal-400'
                  : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
              } border`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Selection Counter */}
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">
          Select Materials
          {selectedCategory !== 'All' && (
            <span className="text-sm font-normal text-gray-400 ml-2">
              ({selectedCategory})
            </span>
          )}
        </h3>
        <div className="text-sm text-teal-400 font-medium">
          {selectedMaterials.length}/{maxSelection} selected
        </div>
      </div>

      {/* Materials Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {filteredMaterials.map((material) => {
          const selected = isSelected(material.id);
          const disabled = !selected && !canSelect;

          return (
            <button
              key={material.id}
              onClick={() => handleMaterialClick(material.id)}
              disabled={disabled}
              className={`
                group flex flex-col items-center p-3 rounded-xl border-2 transition-all duration-200 shadow-[0_4px_8px_rgba(0,0,0,0.3)]
                ${selected
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40'
                  : disabled
                    ? 'border-gray-700 bg-gray-900/50 opacity-50 cursor-not-allowed'
                    : 'border-gray-700 bg-gray-900 active:scale-95'
                }
              `}
              title={`${material.name} (${material.category})`}
            >
              {/* Material Icon */}
              <div className={`mb-2 transition-transform duration-200 ${
                selected ? 'scale-110' : ''
              }`}>
                {getUniqueMaterialIcon(material.name)}
              </div>

              {/* Material Name */}
              <span className={`text-xs font-medium text-center leading-tight ${
                selected ? 'text-teal-400' : 'text-white'
              }`}>
                {material.name}
              </span>

              {/* Category Badge */}
              <span className={`text-xs mt-1 px-2 py-0.5 rounded-full ${
                selected 
                  ? 'bg-teal-500/20 text-teal-300' 
                  : 'bg-gray-700 text-gray-400'
              }`}>
                {material.category}
              </span>

              {/* Selection Indicator */}
              {selected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* No Materials Message */}
      {filteredMaterials.length === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 text-lg mb-2">No materials found</div>
          <div className="text-gray-500 text-sm">
            Try selecting a different category
          </div>
        </div>
      )}

      {/* Selection Limit Warning */}
      {selectedMaterials.length >= maxSelection && (
        <div className="mt-4 p-3 bg-amber-500/10 border border-amber-500/30 rounded-lg">
          <div className="text-amber-400 text-sm font-medium">
            Maximum materials selected ({maxSelection})
          </div>
          <div className="text-amber-300 text-xs mt-1">
            Deselect a material to choose a different one
          </div>
        </div>
      )}
    </div>
  );
};

// Complete materials data from database schema (87 materials)
export const SAMPLE_MATERIALS: Material[] = [
  // Wood Materials (12 total)
  { id: 1, name: 'Oak Wood', category: 'Wood' },
  { id: 2, name: 'Walnut Wood', category: 'Wood' },
  { id: 3, name: 'Cherry Wood', category: 'Wood' },
  { id: 4, name: 'Maple Wood', category: 'Wood' },
  { id: 5, name: 'Birch Wood', category: 'Wood' },
  { id: 6, name: 'Pine Wood', category: 'Wood' },
  { id: 7, name: 'Mahogany Wood', category: 'Wood' },
  { id: 8, name: 'Teak Wood', category: 'Wood' },
  { id: 9, name: 'Bamboo', category: 'Wood' },
  { id: 10, name: 'Reclaimed Wood', category: 'Wood' },
  { id: 11, name: 'Driftwood', category: 'Wood' },
  { id: 12, name: 'Ebony Wood', category: 'Wood' },

  // Metal Materials (12 total)
  { id: 13, name: 'Stainless Steel', category: 'Metal' },
  { id: 14, name: 'Chrome', category: 'Metal' },
  { id: 15, name: 'Brass', category: 'Metal' },
  { id: 16, name: 'Copper', category: 'Metal' },
  { id: 17, name: 'Bronze', category: 'Metal' },
  { id: 18, name: 'Iron', category: 'Metal' },
  { id: 19, name: 'Aluminum', category: 'Metal' },
  { id: 20, name: 'Gold Finish', category: 'Metal' },
  { id: 21, name: 'Silver Finish', category: 'Metal' },
  { id: 22, name: 'Pewter', category: 'Metal' },
  { id: 23, name: 'Titanium', category: 'Metal' },
  { id: 24, name: 'Wrought Iron', category: 'Metal' },

  // Stone Materials (12 total)
  { id: 25, name: 'Marble', category: 'Stone' },
  { id: 26, name: 'Granite', category: 'Stone' },
  { id: 27, name: 'Limestone', category: 'Stone' },
  { id: 28, name: 'Travertine', category: 'Stone' },
  { id: 29, name: 'Slate', category: 'Stone' },
  { id: 30, name: 'Quartzite', category: 'Stone' },
  { id: 31, name: 'Sandstone', category: 'Stone' },
  { id: 32, name: 'Onyx', category: 'Stone' },
  { id: 33, name: 'Basalt', category: 'Stone' },
  { id: 34, name: 'River Rock', category: 'Stone' },
  { id: 35, name: 'Fieldstone', category: 'Stone' },
  { id: 36, name: 'Cobblestone', category: 'Stone' },

  // Fabric Materials (14 total)
  { id: 37, name: 'Linen', category: 'Fabric' },
  { id: 38, name: 'Cotton', category: 'Fabric' },
  { id: 39, name: 'Velvet', category: 'Fabric' },
  { id: 40, name: 'Silk', category: 'Fabric' },
  { id: 41, name: 'Wool', category: 'Fabric' },
  { id: 42, name: 'Cashmere', category: 'Fabric' },
  { id: 43, name: 'Leather', category: 'Fabric' },
  { id: 44, name: 'Suede', category: 'Fabric' },
  { id: 45, name: 'Chenille', category: 'Fabric' },
  { id: 46, name: 'Tweed', category: 'Fabric' },
  { id: 47, name: 'Burlap', category: 'Fabric' },
  { id: 48, name: 'Canvas', category: 'Fabric' },
  { id: 49, name: 'Denim', category: 'Fabric' },
  { id: 50, name: 'Faux Fur', category: 'Fabric' },

  // Glass Materials (8 total)
  { id: 51, name: 'Clear Glass', category: 'Glass' },
  { id: 52, name: 'Frosted Glass', category: 'Glass' },
  { id: 53, name: 'Tempered Glass', category: 'Glass' },
  { id: 54, name: 'Stained Glass', category: 'Glass' },
  { id: 55, name: 'Mirrored Glass', category: 'Glass' },
  { id: 56, name: 'Textured Glass', category: 'Glass' },
  { id: 57, name: 'Colored Glass', category: 'Glass' },
  { id: 58, name: 'Laminated Glass', category: 'Glass' },

  // Ceramic Materials (6 total)
  { id: 59, name: 'Porcelain', category: 'Ceramic' },
  { id: 60, name: 'Ceramic Tile', category: 'Ceramic' },
  { id: 61, name: 'Terra Cotta', category: 'Ceramic' },
  { id: 62, name: 'Stoneware', category: 'Ceramic' },
  { id: 63, name: 'Earthenware', category: 'Ceramic' },
  { id: 64, name: 'Glazed Ceramic', category: 'Ceramic' },

  // Synthetic Materials (8 total)
  { id: 65, name: 'Acrylic', category: 'Synthetic' },
  { id: 66, name: 'Plastic', category: 'Synthetic' },
  { id: 67, name: 'Vinyl', category: 'Synthetic' },
  { id: 68, name: 'Laminate', category: 'Synthetic' },
  { id: 69, name: 'Fiberglass', category: 'Synthetic' },
  { id: 70, name: 'Carbon Fiber', category: 'Synthetic' },
  { id: 71, name: 'Resin', category: 'Synthetic' },
  { id: 72, name: 'Polyurethane', category: 'Synthetic' },

  // Natural Materials (9 total)
  { id: 73, name: 'Rattan', category: 'Natural' },
  { id: 74, name: 'Wicker', category: 'Natural' },
  { id: 75, name: 'Jute', category: 'Natural' },
  { id: 76, name: 'Sisal', category: 'Natural' },
  { id: 77, name: 'Cork', category: 'Natural' },
  { id: 78, name: 'Seagrass', category: 'Natural' },
  { id: 79, name: 'Hemp', category: 'Natural' },
  { id: 80, name: 'Cane', category: 'Natural' },
  { id: 81, name: 'Rush', category: 'Natural' },

  // Composite Materials (6 total)
  { id: 82, name: 'Quartz Composite', category: 'Composite' },
  { id: 83, name: 'Engineered Stone', category: 'Composite' },
  { id: 84, name: 'Concrete', category: 'Composite' },
  { id: 85, name: 'Terrazzo', category: 'Composite' },
  { id: 86, name: 'Corian', category: 'Composite' },
  { id: 87, name: 'Recycled Glass', category: 'Composite' },

  // Sculpture Materials (15 total)
  { id: 88, name: 'Bronze Sculpture', category: 'Sculpture' },
  { id: 89, name: 'Marble Sculpture', category: 'Sculpture' },
  { id: 90, name: 'Clay Sculpture', category: 'Sculpture' },
  { id: 91, name: 'Ceramic Sculpture', category: 'Sculpture' },
  { id: 92, name: 'Wood Carving', category: 'Sculpture' },
  { id: 93, name: 'Stone Carving', category: 'Sculpture' },
  { id: 94, name: 'Metal Sculpture', category: 'Sculpture' },
  { id: 95, name: 'Glass Sculpture', category: 'Sculpture' },
  { id: 96, name: 'Resin Sculpture', category: 'Sculpture' },
  { id: 97, name: 'Plaster Sculpture', category: 'Sculpture' },
  { id: 98, name: 'Wire Sculpture', category: 'Sculpture' },
  { id: 99, name: 'Paper Sculpture', category: 'Sculpture' },
  { id: 100, name: 'Ice Sculpture', category: 'Sculpture' },
  { id: 101, name: 'Sand Sculpture', category: 'Sculpture' },
  { id: 102, name: 'Mixed Media Sculpture', category: 'Sculpture' },
];

export default MaterialSelector;
