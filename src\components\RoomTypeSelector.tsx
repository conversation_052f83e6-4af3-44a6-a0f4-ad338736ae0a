"use client";
import React from 'react';

interface RoomType {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  preview_image?: string;
}

interface RoomTypeSelectorProps {
  selectedRoomType: string | null;
  onRoomTypeSelect: (roomTypeId: string) => void;
  className?: string;
}

const ROOM_TYPES: RoomType[] = [
  {
    id: 'living-room',
    name: 'Living Room',
    description: 'Main living and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 7h18v10c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V7z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 7V5c0-1.1.9-2 2-2h8c1.1 0 2 .9 2 2v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 11h4v4H7z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M13 13h4v2h-4z"/>
      </svg>
    )
  },
  {
    id: 'bedroom',
    name: 'Bedroom',
    description: 'Sleeping and personal space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18v6c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V9c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v3"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 7c0-1.1.9-2 2-2s2 .9 2 2"/>
        <circle cx="6" cy="18" r="1"/>
        <circle cx="18" cy="18" r="1"/>
      </svg>
    )
  },
  {
    id: 'kitchen',
    name: 'Kitchen',
    description: 'Cooking and dining preparation area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 3v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17 3v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 9h18"/>
        <circle cx="8" cy="14" r="1"/>
        <circle cx="12" cy="14" r="1"/>
        <circle cx="16" cy="14" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 18h8"/>
      </svg>
    )
  },
  {
    id: 'bathroom',
    name: 'Bathroom',
    description: 'Personal hygiene and bathing space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 12h16v6c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 12V8c0-2.2 1.8-4 4-4h4c2.2 0 4 1.8 4 4v4"/>
        <circle cx="8" cy="8" r="1"/>
        <circle cx="16" cy="8" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 16v2"/>
      </svg>
    )
  },
  {
    id: 'dining-room',
    name: 'Dining Room',
    description: 'Formal dining and entertaining space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <ellipse cx="12" cy="12" rx="8" ry="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 10v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 10v4"/>
        <circle cx="8" cy="12" r="1"/>
        <circle cx="12" cy="12" r="1"/>
        <circle cx="16" cy="12" r="1"/>
      </svg>
    )
  },
  {
    id: 'home-office',
    name: 'Home Office',
    description: 'Work and study space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="2" y="4" width="20" height="12" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 20h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 16v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 8h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 11h6"/>
      </svg>
    )
  },
  {
    id: 'kids-room',
    name: 'Kids Room',
    description: 'Children\'s bedroom and play area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18v6c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V9c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v3"/>
        <circle cx="8" cy="8" r="2"/>
        <circle cx="16" cy="8" r="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 15h4"/>
        <circle cx="6" cy="18" r="1"/>
        <circle cx="18" cy="18" r="1"/>
      </svg>
    )
  },
  {
    id: 'media-room',
    name: 'Media Room',
    description: 'Entertainment and media consumption space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="2" y="4" width="20" height="12" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 20h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 16v4"/>
        <polygon points="10,8 10,14 16,11"/>
      </svg>
    )
  },
  {
    id: 'mudroom',
    name: 'Mudroom',
    description: 'Entry and storage space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="8" width="18" height="12" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 8V6c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 12v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 12v4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 12v4"/>
      </svg>
    )
  },
  {
    id: 'patio',
    name: 'Patio',
    description: 'Outdoor living and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 2v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 20v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.93 4.93l1.41 1.41"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17.66 17.66l1.41 1.41"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M2 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M20 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6.34 17.66l-1.41 1.41"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M19.07 4.93l-1.41 1.41"/>
      </svg>
    )
  },
  {
    id: 'laundry-room',
    name: 'Laundry Room',
    description: 'Washing and utility space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="4" y="4" width="16" height="16" rx="2"/>
        <circle cx="8" cy="8" r="1"/>
        <circle cx="12" cy="8" r="1"/>
        <circle cx="12" cy="14" r="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 14c0-1.1.9-2 2-2s2 .9 2 2"/>
      </svg>
    )
  },
  {
    id: 'home-gym',
    name: 'Home Gym',
    description: 'Exercise and fitness space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 12h10"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 8v8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17 8v8"/>
        <circle cx="5" cy="12" r="2"/>
        <circle cx="19" cy="12" r="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 10h6v4H9z"/>
      </svg>
    )
  },
  {
    id: 'guest-room',
    name: 'Guest Room',
    description: 'Temporary accommodation space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18v6c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V9c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v3"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 7c0-1.1.9-2 2-2s2 .9 2 2"/>
        <circle cx="6" cy="18" r="1"/>
        <circle cx="18" cy="18" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 8h2v2h-2z"/>
      </svg>
    )
  },
  {
    id: 'walk-in-closet',
    name: 'Walk-in Closet',
    description: 'Clothing storage and dressing area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="3" width="18" height="18" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 3v18"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 3v18"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 8h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 16h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 8h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 16h2"/>
        <circle cx="12" cy="15" r="1"/>
      </svg>
    )
  },
  {
    id: 'basement',
    name: 'Basement',
    description: 'Lower level multipurpose space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3L3 12h2v8h14v-8h2L12 3z"/>
        <rect x="8" y="14" width="8" height="4"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 20h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 14h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 14h2"/>
      </svg>
    )
  },
  {
    id: 'attic',
    name: 'Attic',
    description: 'Upper level storage or living space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3L3 12h2v8h6v-6h2v6h6v-8h2L12 3z"/>
        <rect x="8" y="8" width="8" height="4"/>
        <circle cx="10" cy="10" r="1"/>
        <circle cx="14" cy="10" r="1"/>
      </svg>
    )
  },
  {
    id: 'sunroom',
    name: 'Sunroom',
    description: 'Glass-enclosed seasonal living space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="8" width="18" height="12" rx="2"/>
        <circle cx="12" cy="5" r="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 1v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.22 4.22l1.42 1.42"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M1 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12h2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M18.36 5.64l1.42-1.42"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 14h10"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 17h6"/>
      </svg>
    )
  },
  {
    id: 'library',
    name: 'Library',
    description: 'Reading and book storage space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="4" width="18" height="16" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M11 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M19 4v16"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 8h1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M13 8h1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M17 8h1"/>
      </svg>
    )
  },
  {
    id: 'game-room',
    name: 'Game Room',
    description: 'Recreation and entertainment space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="6" y="6" width="12" height="8" rx="2"/>
        <circle cx="9" cy="9" r="1"/>
        <circle cx="15" cy="9" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12v1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M15 12v1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 15v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 17h8"/>
      </svg>
    )
  },
  {
    id: 'wine-cellar',
    name: 'Wine Cellar',
    description: 'Wine storage and tasting area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 3h8l-1 6v8c0 1.1-.9 2-2 2h-2c-1.1 0-2-.9-2-2V9L8 3z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 3h4"/>
        <circle cx="11" cy="11" r="1"/>
        <circle cx="13" cy="11" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 15h6"/>
      </svg>
    )
  },
  {
    id: 'nursery',
    name: 'Nursery',
    description: 'Baby and infant care space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18v6c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2v-6z"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V9c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v3"/>
        <circle cx="8" cy="8" r="1"/>
        <circle cx="16" cy="8" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 15h4"/>
        <circle cx="6" cy="18" r="1"/>
        <circle cx="18" cy="18" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 5v2"/>
      </svg>
    )
  },
  {
    id: 'pantry',
    name: 'Pantry',
    description: 'Food storage and organization area',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="4" y="8" width="16" height="12" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 8V6c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 15h8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 18h6"/>
      </svg>
    )
  },
  {
    id: 'foyer',
    name: 'Foyer',
    description: 'Entry and welcome space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3L3 12h2v8h6v-6h2v6h6v-8h2L12 3z"/>
        <circle cx="12" cy="8" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 16h6"/>
      </svg>
    )
  },
  {
    id: 'hallway',
    name: 'Hallway',
    description: 'Connecting passage space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="8" width="18" height="8" rx="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 12h18"/>
        <circle cx="6" cy="10" r="1"/>
        <circle cx="18" cy="10" r="1"/>
        <circle cx="6" cy="14" r="1"/>
        <circle cx="18" cy="14" r="1"/>
      </svg>
    )
  },
  {
    id: 'balcony',
    name: 'Balcony',
    description: 'Outdoor extension space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <rect x="3" y="12" width="18" height="8" rx="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 12V8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M10 12V8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M14 12V8"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M18 12V8"/>
        <circle cx="12" cy="5" r="2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 1v2"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 16h10"/>
      </svg>
    )
  },
  {
    id: 'garage',
    name: 'Garage',
    description: 'Vehicle storage and workshop space',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="1.5" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M7 17h10l1-6H6l1 6z"/>
        <circle cx="9" cy="19" r="1"/>
        <circle cx="15" cy="19" r="1"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 11L5 6H3"/>
        <path strokeLinecap="round" strokeLinejoin="round" d="M3 21h18"/>
        <rect x="2" y="3" width="20" height="2"/>
      </svg>
    )
  }
];

export const RoomTypeSelector: React.FC<RoomTypeSelectorProps> = ({
  selectedRoomType,
  onRoomTypeSelect,
  className = ""
}) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Room Type</h3>
        <p className="text-gray-400 text-sm">
          Select the type of room you want to design
        </p>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
        {ROOM_TYPES.map((roomType) => {
          const isSelected = selectedRoomType === roomType.id;
          
          return (
            <button
              key={roomType.id}
              onClick={() => onRoomTypeSelect(roomType.id)}
              className={`
                group flex flex-col items-center p-3 rounded-xl border-2 transition-all duration-200 relative
                ${isSelected
                  ? 'border-teal-500 bg-teal-500/10 ring-2 ring-teal-300/40'
                  : 'border-gray-700 bg-gray-900 hover:border-teal-500 hover:bg-gray-800 active:scale-95'
                }
              `}
              title={roomType.description}
            >
              {/* Room Icon */}
              <div className={`mb-3 transition-all duration-200 ${
                isSelected
                  ? 'scale-110 text-teal-400 drop-shadow-lg'
                  : 'text-gray-400 group-hover:text-teal-400 group-hover:scale-105 group-hover:drop-shadow-[0_0_8px_rgba(20,184,166,0.6)]'
              }`}>
                {roomType.icon}
              </div>

              {/* Room Name */}
              <span className={`text-xs font-medium text-center leading-tight ${
                isSelected ? 'text-teal-400' : 'text-white'
              }`}>
                {roomType.name}
              </span>

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Selected Room Info */}
      {selectedRoomType && (
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700">
          {(() => {
            const selectedRoom = ROOM_TYPES.find(room => room.id === selectedRoomType);
            if (!selectedRoom) return null;
            
            return (
              <div className="flex items-center gap-4">
                <div className="text-teal-400 w-8 h-8 flex items-center justify-center">
                  {selectedRoom.icon}
                </div>
                <div>
                  <h4 className="text-white font-medium">{selectedRoom.name}</h4>
                  <p className="text-gray-400 text-sm">{selectedRoom.description}</p>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
};

export default RoomTypeSelector;
export { ROOM_TYPES };
export type { RoomType };
