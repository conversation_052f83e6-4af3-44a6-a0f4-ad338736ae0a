"use client";
import React from 'react';
import { STYLES, CATEGORY_LABELS } from './StyleSelector';

interface StyleCardSelectorProps {
  selectedStyles: number[];
  onStyleToggle: (styleId: number) => void;
  maxSelection?: number;
  className?: string;
}

export const StyleCardSelector: React.FC<StyleCardSelectorProps> = ({
  selectedStyles,
  onStyleToggle,
  maxSelection = 5,
  className = ""
}) => {
  const [selectedCategory, setSelectedCategory] = React.useState<string>('all');

  const filteredStyles = selectedCategory === 'all' 
    ? STYLES 
    : STYLES.filter(style => style.category === selectedCategory);

  const isSelected = (styleId: number) => selectedStyles.includes(styleId);
  const canSelect = selectedStyles.length < maxSelection;

  const handleStyleClick = (styleId: number) => {
    if (isSelected(styleId) || canSelect) {
      onStyleToggle(styleId);
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Choose Design Styles</h3>
        <p className="text-gray-400 text-sm">
          Select up to {maxSelection} styles to blend together ({selectedStyles.length}/{maxSelection} selected)
        </p>
      </div>

      {/* Category Filter */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
              selectedCategory === 'all'
                ? 'bg-teal-600 text-white border-teal-400'
                : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
            }`}
          >
            All Styles
          </button>
          {Object.entries(CATEGORY_LABELS).map(([key, label]) => (
            <button
              key={key}
              onClick={() => setSelectedCategory(key)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all border ${
                selectedCategory === key
                  ? 'bg-teal-600 text-white border-teal-400'
                  : 'bg-gray-800 text-gray-300 border-gray-700 hover:bg-gray-700'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Styles Grid - Card Layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredStyles.map((style) => {
          const selected = isSelected(style.id);
          const disabled = !selected && !canSelect;

          return (
            <button
              key={style.id}
              onClick={() => handleStyleClick(style.id)}
              disabled={disabled}
              className={`
                group relative bg-gray-900 rounded-2xl overflow-hidden border-2 transition-all duration-300 text-left
                ${selected 
                  ? 'border-teal-500 ring-4 ring-teal-300/40 scale-105' 
                  : disabled
                    ? 'border-gray-700 bg-gray-900/50 opacity-50 cursor-not-allowed'
                    : 'border-gray-700 hover:border-teal-500 hover:scale-105 hover:shadow-xl'
                }
              `}
            >
              {/* Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={style.image}
                  alt={style.label}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  draggable={false}
                />
                
                {/* Selection Indicator */}
                {selected && (
                  <div className="absolute top-3 right-3 w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8.586 10l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}

                {/* Selection Number */}
                {selected && (
                  <div className="absolute top-3 left-3 w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                    {selectedStyles.indexOf(style.id) + 1}
                  </div>
                )}

                {/* Category Badge */}
                <div className="absolute bottom-3 left-3">
                  <span className="text-xs px-2 py-1 rounded-full bg-black/60 text-white backdrop-blur-sm">
                    {CATEGORY_LABELS[style.category as keyof typeof CATEGORY_LABELS]}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-6 h-6 bg-gray-800 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-teal-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                    </svg>
                  </div>
                  <h3 className={`font-semibold text-lg ${
                    selected ? 'text-teal-400' : 'text-white'
                  }`}>
                    {style.label}
                  </h3>
                </div>
                
                <p className="text-gray-400 text-sm leading-relaxed">
                  {style.description}
                </p>
              </div>

              {/* Hover Overlay */}
              {!selected && !disabled && (
                <div className="absolute inset-0 bg-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              )}
            </button>
          );
        })}
      </div>

      {/* Selection Summary */}
      {selectedStyles.length > 0 && (
        <div className="mt-8 p-6 bg-gray-800/30 rounded-xl border border-gray-700">
          <h4 className="text-white font-medium mb-4">Selected Styles ({selectedStyles.length})</h4>
          <div className="flex flex-wrap gap-3">
            {selectedStyles.map((styleId, index) => {
              const style = STYLES.find(s => s.id === styleId);
              if (!style) return null;
              
              return (
                <div key={styleId} className="flex items-center gap-3 bg-teal-500/20 text-teal-300 px-4 py-2 rounded-lg border border-teal-500/30">
                  <span className="w-6 h-6 bg-teal-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    {index + 1}
                  </span>
                  <span className="font-medium">{style.label}</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onStyleToggle(styleId);
                    }}
                    className="ml-2 hover:bg-teal-400/20 rounded-full p-1 transition-colors"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Selection Limit Warning */}
      {selectedStyles.length >= maxSelection && (
        <div className="mt-4 p-4 bg-amber-500/10 border border-amber-500/30 rounded-lg">
          <div className="text-amber-400 text-sm font-medium">
            Maximum styles selected ({maxSelection})
          </div>
          <div className="text-amber-300 text-xs mt-1">
            Remove a style to select a different one
          </div>
        </div>
      )}
    </div>
  );
};

export default StyleCardSelector;
