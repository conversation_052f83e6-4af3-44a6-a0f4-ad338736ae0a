import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
  color?: string;
}

// WOOD MATERIALS - Each with unique grain patterns and colors

// Oak Wood - Strong vertical grain
export const OakWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="oakGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#DEB887"/>
        <stop offset="50%" stopColor="#F4A460"/>
        <stop offset="100%" stopColor="#DEB887"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#oakGradient)" rx="2"/>
    <path d="M4,0 L4,24 M8,0 L8,24 M12,0 L12,24 M16,0 L16,24 M20,0 L20,24" stroke="#CD853F" strokeWidth="0.5" opacity="0.6"/>
    <path d="M6,2 Q8,6 6,10 Q8,14 6,18 Q8,22 6,24" stroke="#8B7355" strokeWidth="0.3" fill="none"/>
    <path d="M14,0 Q16,4 14,8 Q16,12 14,16 Q16,20 14,24" stroke="#8B7355" strokeWidth="0.3" fill="none"/>
  </svg>
);

// Walnut Wood - Rich dark grain with swirls
export const WalnutWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="walnutGradient" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#A0522D"/>
        <stop offset="70%" stopColor="#8B4513"/>
        <stop offset="100%" stopColor="#654321"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#walnutGradient)" rx="2"/>
    <path d="M2,12 Q6,8 12,12 Q18,16 22,12" stroke="#654321" strokeWidth="1" fill="none"/>
    <path d="M0,6 Q8,10 16,6 Q24,2 24,8" stroke="#3C1810" strokeWidth="0.8" fill="none"/>
    <path d="M0,18 Q8,14 16,18 Q24,22 24,16" stroke="#3C1810" strokeWidth="0.8" fill="none"/>
    <ellipse cx="8" cy="8" rx="3" ry="1.5" fill="#3C1810" opacity="0.4"/>
    <ellipse cx="16" cy="16" rx="2" ry="1" fill="#3C1810" opacity="0.3"/>
  </svg>
);

// Cherry Wood - Reddish with fine grain
export const CherryWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="cherryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D2691E"/>
        <stop offset="50%" stopColor="#CD853F"/>
        <stop offset="100%" stopColor="#A0522D"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#cherryGradient)" rx="2"/>
    <path d="M0,4 Q12,2 24,4 M0,8 Q12,6 24,8 M0,12 Q12,10 24,12 M0,16 Q12,14 24,16 M0,20 Q12,18 24,20"
          stroke="#8B4513" strokeWidth="0.4" fill="none" opacity="0.7"/>
    <circle cx="6" cy="6" r="1" fill="#8B4513" opacity="0.5"/>
    <circle cx="18" cy="14" r="0.8" fill="#8B4513" opacity="0.4"/>
    <circle cx="10" cy="18" r="0.6" fill="#8B4513" opacity="0.3"/>
  </svg>
);

// Maple Wood - Light with subtle grain
export const MapleWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="mapleGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stopColor="#F5DEB3"/>
        <stop offset="50%" stopColor="#F4A460"/>
        <stop offset="100%" stopColor="#DEB887"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#mapleGradient)" rx="2"/>
    <path d="M2,0 L2,24 M6,0 L6,24 M10,0 L10,24 M14,0 L14,24 M18,0 L18,24 M22,0 L22,24"
          stroke="#DDD26A" strokeWidth="0.3" opacity="0.4"/>
    <path d="M0,8 Q4,6 8,8 Q12,10 16,8 Q20,6 24,8" stroke="#D2B48C" strokeWidth="0.5" fill="none"/>
    <path d="M0,16 Q4,14 8,16 Q12,18 16,16 Q20,14 24,16" stroke="#D2B48C" strokeWidth="0.5" fill="none"/>
  </svg>
);

// Birch Wood - White with dark horizontal marks
export const BirchWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5DEB3" rx="2"/>
    <rect x="0" y="4" width="24" height="1" fill="#2F2F2F" opacity="0.8"/>
    <rect x="0" y="8" width="16" height="0.5" fill="#2F2F2F" opacity="0.6"/>
    <rect x="0" y="12" width="20" height="1.5" fill="#2F2F2F" opacity="0.7"/>
    <rect x="0" y="16" width="12" height="0.8" fill="#2F2F2F" opacity="0.5"/>
    <rect x="0" y="20" width="24" height="1" fill="#2F2F2F" opacity="0.8"/>
    <ellipse cx="8" cy="6" rx="2" ry="0.5" fill="#2F2F2F" opacity="0.3"/>
    <ellipse cx="16" cy="14" rx="1.5" ry="0.3" fill="#2F2F2F" opacity="0.3"/>
  </svg>
);

// Pine Wood - Light with knots
export const PineWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="pineKnot1" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#8B4513"/>
        <stop offset="100%" stopColor="#DDD26A"/>
      </radialGradient>
      <radialGradient id="pineKnot2" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#A0522D"/>
        <stop offset="100%" stopColor="#DDD26A"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="#DDD26A" rx="2"/>
    <path d="M0,0 L24,0 M0,6 L24,6 M0,12 L24,12 M0,18 L24,18 M0,24 L24,24"
          stroke="#F4A460" strokeWidth="0.5" opacity="0.6"/>
    <circle cx="8" cy="8" r="2.5" fill="url(#pineKnot1)"/>
    <circle cx="16" cy="16" r="1.8" fill="url(#pineKnot2)"/>
    <circle cx="8" cy="8" r="1" fill="#654321"/>
    <circle cx="16" cy="16" r="0.7" fill="#654321"/>
  </svg>
);

// Mahogany Wood - Deep red-brown with rich grain
export const MahoganyWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="mahoganyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#C04000"/>
        <stop offset="50%" stopColor="#8B0000"/>
        <stop offset="100%" stopColor="#654321"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#mahoganyGradient)" rx="2"/>
    <path d="M0,6 Q6,4 12,6 Q18,8 24,6 M0,12 Q6,10 12,12 Q18,14 24,12 M0,18 Q6,16 12,18 Q18,20 24,18"
          stroke="#4A0000" strokeWidth="0.8" fill="none"/>
    <path d="M4,0 Q6,8 4,16 Q6,24 8,24 M16,0 Q18,8 16,16 Q18,24 20,24"
          stroke="#4A0000" strokeWidth="0.6" fill="none"/>
  </svg>
);

// Teak Wood - Golden brown with oil-rich appearance
export const TeakWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="teakGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#B8860B"/>
        <stop offset="30%" stopColor="#DAA520"/>
        <stop offset="70%" stopColor="#B8860B"/>
        <stop offset="100%" stopColor="#8B7355"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#teakGradient)" rx="2"/>
    <path d="M2,2 Q12,6 22,2 M2,8 Q12,12 22,8 M2,14 Q12,18 22,14 M2,20 Q12,24 22,20"
          stroke="#8B7355" strokeWidth="0.6" fill="none" opacity="0.7"/>
    <ellipse cx="12" cy="6" rx="4" ry="1" fill="#8B7355" opacity="0.3"/>
    <ellipse cx="12" cy="18" rx="3" ry="0.8" fill="#8B7355" opacity="0.3"/>
    <circle cx="6" cy="12" r="0.8" fill="#FFD700" opacity="0.6"/>
    <circle cx="18" cy="12" r="0.6" fill="#FFD700" opacity="0.5"/>
  </svg>
);

// Bamboo - Segmented with nodes
export const BambooIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="bambooGradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stopColor="#DAA520"/>
        <stop offset="50%" stopColor="#F4A460"/>
        <stop offset="100%" stopColor="#DEB887"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#bambooGradient)" rx="2"/>
    <rect x="0" y="6" width="24" height="2" fill="#8B7355"/>
    <rect x="0" y="12" width="24" height="2" fill="#8B7355"/>
    <rect x="0" y="18" width="24" height="2" fill="#8B7355"/>
    <path d="M4,0 L4,6 M8,0 L8,6 M12,0 L12,6 M16,0 L16,6 M20,0 L20,6" stroke="#B8860B" strokeWidth="0.5"/>
    <path d="M4,8 L4,12 M8,8 L8,12 M12,8 L12,12 M16,8 L16,12 M20,8 L20,12" stroke="#B8860B" strokeWidth="0.5"/>
    <path d="M4,14 L4,18 M8,14 L8,18 M12,14 L12,18 M16,14 L16,18 M20,14 L20,18" stroke="#B8860B" strokeWidth="0.5"/>
    <path d="M4,20 L4,24 M8,20 L8,24 M12,20 L12,24 M16,20 L16,24 M20,20 L20,24" stroke="#B8860B" strokeWidth="0.5"/>
  </svg>
);

// Reclaimed Wood - Weathered with nail holes and distress marks
export const ReclaimedWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="reclaimedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8B7355"/>
        <stop offset="30%" stopColor="#A0522D"/>
        <stop offset="70%" stopColor="#696969"/>
        <stop offset="100%" stopColor="#8B7355"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#reclaimedGradient)" rx="2"/>
    <path d="M2,4 Q8,2 14,4 Q20,6 22,4 M2,12 Q8,10 14,12 Q20,14 22,12 M2,20 Q8,18 14,20 Q20,22 22,20"
          stroke="#654321" strokeWidth="0.8" fill="none"/>
    <circle cx="6" cy="8" r="0.8" fill="#2F2F2F"/>
    <circle cx="18" cy="16" r="0.6" fill="#2F2F2F"/>
    <rect x="10" y="2" width="0.5" height="4" fill="#2F2F2F"/>
    <rect x="14" y="18" width="0.5" height="4" fill="#2F2F2F"/>
    <path d="M4,6 L8,10 M16,14 L20,18" stroke="#2F2F2F" strokeWidth="0.5"/>
  </svg>
);

// Driftwood - Smooth, weathered, gray-brown
export const DriftwoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="driftwoodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D3D3D3"/>
        <stop offset="30%" stopColor="#A0522D"/>
        <stop offset="70%" stopColor="#808080"/>
        <stop offset="100%" stopColor="#696969"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#driftwoodGradient)" rx="4"/>
    <path d="M0,8 Q6,6 12,8 Q18,10 24,8 M0,16 Q6,14 12,16 Q18,18 24,16"
          stroke="#696969" strokeWidth="1" fill="none" opacity="0.6"/>
    <ellipse cx="8" cy="12" rx="3" ry="1.5" fill="#A9A9A9" opacity="0.4"/>
    <ellipse cx="16" cy="12" rx="2" ry="1" fill="#A9A9A9" opacity="0.3"/>
    <path d="M4,4 Q8,8 12,4 Q16,8 20,4" stroke="#A9A9A9" strokeWidth="0.5" fill="none"/>
  </svg>
);

// Ebony Wood - Very dark, almost black with subtle grain
export const EbonyWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="ebonyGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#2F2F2F"/>
        <stop offset="50%" stopColor="#1C1C1C"/>
        <stop offset="100%" stopColor="#0F0F0F"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#ebonyGradient)" rx="2"/>
    <path d="M4,0 L4,24 M8,0 L8,24 M12,0 L12,24 M16,0 L16,24 M20,0 L20,24"
          stroke="#404040" strokeWidth="0.3" opacity="0.5"/>
    <path d="M0,6 Q12,4 24,6 M0,12 Q12,10 24,12 M0,18 Q12,16 24,18"
          stroke="#404040" strokeWidth="0.2" fill="none" opacity="0.3"/>
    <ellipse cx="12" cy="8" rx="2" ry="0.5" fill="#404040" opacity="0.4"/>
    <ellipse cx="12" cy="16" rx="1.5" ry="0.3" fill="#404040" opacity="0.3"/>
  </svg>
);

// METAL MATERIALS - Each with unique finishes and textures

// Stainless Steel - Brushed finish with reflections
export const StainlessSteelIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="steelGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="#C0C0C0"/>
        <stop offset="25%" stopColor="#E5E5E5"/>
        <stop offset="50%" stopColor="#C0C0C0"/>
        <stop offset="75%" stopColor="#A8A8A8"/>
        <stop offset="100%" stopColor="#C0C0C0"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#steelGradient)" rx="2"/>
    <path d="M0,0 L24,0 M0,2 L24,2 M0,4 L24,4 M0,6 L24,6 M0,8 L24,8 M0,10 L24,10 M0,12 L24,12 M0,14 L24,14 M0,16 L24,16 M0,18 L24,18 M0,20 L24,20 M0,22 L24,22 M0,24 L24,24"
          stroke="#FFFFFF" strokeWidth="0.1" opacity="0.3"/>
    <rect x="6" y="4" width="12" height="2" fill="#FFFFFF" opacity="0.4"/>
    <rect x="4" y="18" width="16" height="1" fill="#FFFFFF" opacity="0.3"/>
  </svg>
);

// Chrome - Mirror-like with sharp reflections
export const ChromeIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="chromeGradient" cx="30%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="30%" stopColor="#E5E5E5"/>
        <stop offset="70%" stopColor="#C0C0C0"/>
        <stop offset="100%" stopColor="#A8A8A8"/>
      </radialGradient>
      <linearGradient id="chromeReflection" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="50%" stopColor="transparent"/>
        <stop offset="100%" stopColor="#000000" stopOpacity="0.2"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#chromeGradient)" rx="2"/>
    <rect width="24" height="24" fill="url(#chromeReflection)" rx="2"/>
    <ellipse cx="8" cy="6" rx="3" ry="1.5" fill="#FFFFFF" opacity="0.7"/>
    <ellipse cx="16" cy="18" rx="2" ry="1" fill="#000000" opacity="0.2"/>
  </svg>
);

// Brass - Golden with patina
export const BrassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="brassGradient" cx="50%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFD700"/>
        <stop offset="40%" stopColor="#B5A642"/>
        <stop offset="80%" stopColor="#8B7355"/>
        <stop offset="100%" stopColor="#6B5B47"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#brassGradient)" rx="2"/>
    <circle cx="12" cy="8" r="3" fill="#FFD700" opacity="0.6"/>
    <path d="M4,4 Q12,2 20,4 Q20,8 12,6 Q4,8 4,4" fill="#8B7355" opacity="0.3"/>
    <path d="M2,16 Q8,14 14,16 Q20,18 22,16" stroke="#6B5B47" strokeWidth="1" fill="none"/>
    <circle cx="6" cy="18" r="1" fill="#6B5B47" opacity="0.4"/>
    <circle cx="18" cy="20" r="0.8" fill="#6B5B47" opacity="0.3"/>
  </svg>
);

// Wood Materials Icon
export const WoodIcon: React.FC<IconProps> = ({ size = 24, color = "#8B4513", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="woodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor={color} stopOpacity="1"/>
        <stop offset="50%" stopColor="#DEB887" stopOpacity="0.8"/>
        <stop offset="100%" stopColor={color} stopOpacity="0.6"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#woodGradient)" rx="2"/>
    <path d="M2,6 Q12,4 22,6" stroke="#654321" strokeWidth="1" fill="none" opacity="0.6"/>
    <path d="M2,12 Q12,10 22,12" stroke="#654321" strokeWidth="1" fill="none" opacity="0.5"/>
    <path d="M2,18 Q12,16 22,18" stroke="#654321" strokeWidth="1" fill="none" opacity="0.4"/>
    <circle cx="6" cy="8" r="1" fill="#A0522D" opacity="0.7"/>
    <circle cx="18" cy="14" r="0.8" fill="#A0522D" opacity="0.6"/>
  </svg>
);

// Metal Materials Icon
export const MetalIcon: React.FC<IconProps> = ({ size = 24, color = "#C0C0C0", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="metalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor={color} stopOpacity="1"/>
        <stop offset="30%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="70%" stopColor={color} stopOpacity="0.6"/>
        <stop offset="100%" stopColor="#000000" stopOpacity="0.3"/>
      </linearGradient>
      <linearGradient id="metalShine" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="transparent"/>
        <stop offset="45%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="55%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="100%" stopColor="transparent"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#metalGradient)" rx="2"/>
    <rect x="8" y="0" width="8" height="24" fill="url(#metalShine)" rx="1"/>
    <circle cx="6" cy="6" r="1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="18" cy="18" r="1" fill="#FFFFFF" opacity="0.6"/>
  </svg>
);

// Stone Materials Icon
export const StoneIcon: React.FC<IconProps> = ({ size = 24, color = "#808080", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <filter id="stoneTexture">
        <feTurbulence baseFrequency="0.9" numOctaves="4" result="noise"/>
        <feDisplacementMap in="SourceGraphic" in2="noise" scale="1"/>
      </filter>
    </defs>
    <rect width="24" height="24" fill={color} rx="3"/>
    <path d="M0,8 L8,0 L16,0 L24,8 L24,16 L16,24 L8,24 L0,16 Z" fill={color} opacity="0.8"/>
    <circle cx="7" cy="7" r="2" fill={color} opacity="0.6" filter="url(#stoneTexture)"/>
    <circle cx="17" cy="12" r="1.5" fill={color} opacity="0.4"/>
    <circle cx="12" cy="18" r="1" fill={color} opacity="0.5"/>
    <path d="M3,3 L6,6 M18,3 L21,6 M3,21 L6,18 M18,21 L21,18" stroke={color} strokeWidth="1" opacity="0.3"/>
  </svg>
);

// Fabric Materials Icon
export const FabricIcon: React.FC<IconProps> = ({ size = 24, color = "#DEB887", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="fabricWeave" patternUnits="userSpaceOnUse" width="4" height="4">
        <rect width="2" height="2" fill={color}/>
        <rect x="2" y="2" width="2" height="2" fill={color}/>
        <rect x="2" y="0" width="2" height="2" fill={color} opacity="0.7"/>
        <rect x="0" y="2" width="2" height="2" fill={color} opacity="0.7"/>
      </pattern>
    </defs>
    <rect width="24" height="24" fill="url(#fabricWeave)" rx="2"/>
    <path d="M0,0 L24,0 M0,6 L24,6 M0,12 L24,12 M0,18 L24,18 M0,24 L24,24" 
          stroke={color} strokeWidth="0.5" opacity="0.3"/>
    <path d="M0,0 L0,24 M6,0 L6,24 M12,0 L12,24 M18,0 L18,24 M24,0 L24,24" 
          stroke={color} strokeWidth="0.5" opacity="0.3"/>
  </svg>
);

// Glass Materials Icon
export const GlassIcon: React.FC<IconProps> = ({ size = 24, color = "#E6F3FF", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor={color} stopOpacity="0.8"/>
        <stop offset="50%" stopColor="#FFFFFF" stopOpacity="0.4"/>
        <stop offset="100%" stopColor={color} stopOpacity="0.6"/>
      </linearGradient>
      <linearGradient id="glassReflection" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="transparent"/>
        <stop offset="30%" stopColor="#FFFFFF" stopOpacity="0.9"/>
        <stop offset="70%" stopColor="#FFFFFF" stopOpacity="0.9"/>
        <stop offset="100%" stopColor="transparent"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#glassGradient)" rx="2" stroke="#B3D9FF" strokeWidth="1"/>
    <rect x="4" y="2" width="16" height="4" fill="url(#glassReflection)" rx="1"/>
    <circle cx="18" cy="18" r="2" fill="#FFFFFF" opacity="0.7"/>
    <path d="M2,2 L6,6 M18,2 L22,6" stroke="#FFFFFF" strokeWidth="1" opacity="0.5"/>
  </svg>
);

// Ceramic Materials Icon
export const CeramicIcon: React.FC<IconProps> = ({ size = 24, color = "#F5DEB3", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="ceramicGradient" cx="50%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="50%" stopColor={color} stopOpacity="0.9"/>
        <stop offset="100%" stopColor={color} stopOpacity="1"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#ceramicGradient)" rx="2"/>
    <ellipse cx="12" cy="8" rx="8" ry="2" fill="#FFFFFF" opacity="0.4"/>
    <path d="M4,4 Q12,2 20,4 Q20,8 12,10 Q4,8 4,4" fill="#FFFFFF" opacity="0.3"/>
    <circle cx="8" cy="16" r="1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="16" cy="20" r="0.5" fill="#FFFFFF" opacity="0.5"/>
  </svg>
);

// Synthetic Materials Icon
export const SyntheticIcon: React.FC<IconProps> = ({ size = 24, color = "#FF6B6B", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="syntheticGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor={color}/>
        <stop offset="50%" stopColor="#FFFFFF" stopOpacity="0.3"/>
        <stop offset="100%" stopColor={color} stopOpacity="0.8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#syntheticGradient)" rx="2"/>
    <polygon points="12,2 22,8 22,16 12,22 2,16 2,8" fill={color} opacity="0.7"/>
    <polygon points="12,6 18,9 18,15 12,18 6,15 6,9" fill="#FFFFFF" opacity="0.4"/>
    <circle cx="12" cy="12" r="3" fill={color} opacity="0.8"/>
    <circle cx="12" cy="12" r="1" fill="#FFFFFF"/>
  </svg>
);

// Natural Materials Icon
export const NaturalIcon: React.FC<IconProps> = ({ size = 24, color = "#8FBC8F", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="naturalWeave" patternUnits="userSpaceOnUse" width="6" height="6">
        <path d="M0,3 Q3,0 6,3 Q3,6 0,3" fill={color} opacity="0.6"/>
        <path d="M3,0 Q6,3 3,6 Q0,3 3,0" fill={color} opacity="0.4"/>
      </pattern>
    </defs>
    <rect width="24" height="24" fill="url(#naturalWeave)" rx="2"/>
    <path d="M2,2 Q6,6 10,2 Q14,6 18,2 Q22,6 22,10 Q18,14 22,18 Q18,22 14,18 Q10,22 6,18 Q2,22 2,18 Q6,14 2,10 Q6,6 2,2" 
          stroke={color} strokeWidth="1" fill="none" opacity="0.5"/>
    <circle cx="6" cy="6" r="1" fill={color} opacity="0.8"/>
    <circle cx="18" cy="18" r="1" fill={color} opacity="0.8"/>
    <circle cx="18" cy="6" r="0.5" fill={color} opacity="0.6"/>
    <circle cx="6" cy="18" r="0.5" fill={color} opacity="0.6"/>
  </svg>
);

// Composite Materials Icon
export const CompositeIcon: React.FC<IconProps> = ({ size = 24, color = "#9370DB", className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <pattern id="compositePattern" patternUnits="userSpaceOnUse" width="8" height="8">
        <rect width="4" height="4" fill={color}/>
        <rect x="4" y="4" width="4" height="4" fill={color}/>
        <rect x="4" y="0" width="4" height="4" fill={color} opacity="0.7"/>
        <rect x="0" y="4" width="4" height="4" fill={color} opacity="0.7"/>
      </pattern>
      <linearGradient id="compositeShine" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.3"/>
        <stop offset="100%" stopColor="transparent"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#compositePattern)" rx="2"/>
    <rect width="24" height="24" fill="url(#compositeShine)" rx="2"/>
    <path d="M0,12 L12,0 M12,24 L24,12" stroke="#FFFFFF" strokeWidth="1" opacity="0.4"/>
    <circle cx="6" cy="6" r="1.5" fill="#FFFFFF" opacity="0.5"/>
    <circle cx="18" cy="18" r="1.5" fill="#FFFFFF" opacity="0.5"/>
  </svg>
);

// Material Icon Mapper
export const getMaterialIcon = (category: string, materialName: string) => {
  const iconProps = { size: 48, className: "rounded-lg" };
  
  // Define colors for specific materials
  const materialColors: Record<string, string> = {
    // Woods
    'Oak Wood': '#DEB887',
    'Walnut Wood': '#8B4513',
    'Cherry Wood': '#D2691E',
    'Maple Wood': '#F4A460',
    'Birch Wood': '#F5DEB3',
    'Pine Wood': '#DDD26A',
    'Mahogany Wood': '#C04000',
    'Teak Wood': '#B8860B',
    'Bamboo': '#DAA520',
    'Reclaimed Wood': '#8B7355',
    'Driftwood': '#A0522D',
    'Ebony Wood': '#2F2F2F',
    
    // Metals
    'Stainless Steel': '#C0C0C0',
    'Chrome': '#E5E5E5',
    'Brass': '#B5A642',
    'Copper': '#B87333',
    'Bronze': '#CD7F32',
    'Iron': '#464451',
    'Aluminum': '#A8A8A8',
    'Gold Finish': '#FFD700',
    'Silver Finish': '#C0C0C0',
    'Pewter': '#96A8A1',
    'Titanium': '#878681',
    'Wrought Iron': '#2F2F2F',
    
    // Stones
    'Marble': '#F8F8FF',
    'Granite': '#696969',
    'Limestone': '#F5F5DC',
    'Travertine': '#FAEBD7',
    'Slate': '#2F4F4F',
    'Quartzite': '#E6E6FA',
    'Sandstone': '#F4A460',
    'Onyx': '#0F0F0F',
    'Basalt': '#36454F',
    'River Rock': '#708090',
    'Fieldstone': '#8B7D6B',
    'Cobblestone': '#696969'
  };
  
  const color = materialColors[materialName] || getDefaultColorForCategory(category);
  
  switch (category.toLowerCase()) {
    case 'wood':
      return <WoodIcon {...iconProps} color={color} />;
    case 'metal':
      return <MetalIcon {...iconProps} color={color} />;
    case 'stone':
      return <StoneIcon {...iconProps} color={color} />;
    case 'fabric':
      return <FabricIcon {...iconProps} color={color} />;
    case 'glass':
      return <GlassIcon {...iconProps} color={color} />;
    case 'ceramic':
      return <CeramicIcon {...iconProps} color={color} />;
    case 'synthetic':
      return <SyntheticIcon {...iconProps} color={color} />;
    case 'natural':
      return <NaturalIcon {...iconProps} color={color} />;
    case 'composite':
      return <CompositeIcon {...iconProps} color={color} />;
    default:
      return <WoodIcon {...iconProps} color={color} />;
  }
};

const getDefaultColorForCategory = (category: string): string => {
  const defaults: Record<string, string> = {
    wood: '#8B4513',
    metal: '#C0C0C0',
    stone: '#808080',
    fabric: '#DEB887',
    glass: '#E6F3FF',
    ceramic: '#F5DEB3',
    synthetic: '#FF6B6B',
    natural: '#8FBC8F',
    composite: '#9370DB'
  };
  return defaults[category.toLowerCase()] || '#808080';
};
