import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Porcelain - Fine white ceramic with delicate patterns
export const PorcelainIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="porcelainGrad" cx="50%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="50%" stopColor="#F8F8FF"/>
        <stop offset="100%" stopColor="#F0F0F0"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#porcelainGrad)" rx="2"/>
    <path d="M4,4 Q8,2 12,4 Q16,6 20,4 Q22,8 20,12 Q18,16 20,20 Q16,22 12,20 Q8,18 4,20 Q2,16 4,12 Q6,8 4,4" 
          fill="none" stroke="#E6E6FA" strokeWidth="0.8" opacity="0.6"/>
    <path d="M8,8 Q12,6 16,8 Q18,12 16,16 Q12,18 8,16 Q6,12 8,8" 
          fill="none" stroke="#DDA0DD" strokeWidth="0.6" opacity="0.5"/>
    <circle cx="12" cy="12" r="2" fill="none" stroke="#D8BFD8" strokeWidth="0.4" opacity="0.4"/>
    <circle cx="6" cy="6" r="0.5" fill="#E6E6FA" opacity="0.8"/>
    <circle cx="18" cy="6" r="0.4" fill="#DDA0DD" opacity="0.7"/>
    <circle cx="6" cy="18" r="0.6" fill="#D8BFD8" opacity="0.6"/>
    <circle cx="18" cy="18" r="0.3" fill="#E6E6FA" opacity="0.9"/>
    <ellipse cx="12" cy="6" rx="1.5" ry="0.8" fill="#FFFFFF" opacity="0.9"/>
    <ellipse cx="12" cy="18" rx="1.2" ry="0.6" fill="#FFFFFF" opacity="0.8"/>
  </svg>
);

// Ceramic Tile - Square grid pattern
export const CeramicTileIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5DEB3" rx="2"/>
    <rect x="0" y="0" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="6" y="0" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="12" y="0" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="18" y="0" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="0" y="6" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="6" y="6" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="12" y="6" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="18" y="6" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="0" y="12" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="6" y="12" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="12" y="12" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="18" y="12" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="0" y="18" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="6" y="18" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="12" y="18" width="6" height="6" fill="#F0E68C" stroke="#D2B48C" strokeWidth="1"/>
    <rect x="18" y="18" width="6" height="6" fill="#FAEBD7" stroke="#D2B48C" strokeWidth="1"/>
    <circle cx="3" cy="3" r="0.5" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="9" cy="9" r="0.4" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="15" cy="15" r="0.6" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="21" cy="21" r="0.3" fill="#FFFFFF" opacity="0.6"/>
  </svg>
);

// Terra Cotta - Earthy clay with natural imperfections
export const TerraCottaIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="terraCottaGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#CD853F"/>
        <stop offset="30%" stopColor="#D2691E"/>
        <stop offset="70%" stopColor="#A0522D"/>
        <stop offset="100%" stopColor="#8B4513"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#terraCottaGrad)" rx="2"/>
    <ellipse cx="6" cy="4" rx="2.5" ry="1.8" fill="#DEB887" opacity="0.7"/>
    <ellipse cx="18" cy="7" rx="2" ry="2.5" fill="#F4A460" opacity="0.6"/>
    <ellipse cx="4" cy="12" rx="1.8" ry="2.2" fill="#DEB887" opacity="0.8"/>
    <ellipse cx="16" cy="15" rx="2.3" ry="1.5" fill="#F4A460" opacity="0.5"/>
    <ellipse cx="8" cy="20" rx="2.1" ry="1.9" fill="#DEB887" opacity="0.7"/>
    <ellipse cx="20" cy="18" rx="1.6" ry="2.4" fill="#F4A460" opacity="0.6"/>
    <circle cx="10" cy="8" r="0.8" fill="#8B4513" opacity="0.8"/>
    <circle cx="14" cy="12" r="0.6" fill="#654321" opacity="0.7"/>
    <circle cx="12" cy="18" r="0.7" fill="#8B4513" opacity="0.6"/>
    <circle cx="22" cy="10" r="0.5" fill="#654321" opacity="0.8"/>
    <path d="M2,6 Q8,4 14,6 Q20,8 22,6" stroke="#654321" strokeWidth="0.6" fill="none" opacity="0.5"/>
    <path d="M0,14 Q6,12 12,14 Q18,16 24,14" stroke="#654321" strokeWidth="0.6" fill="none" opacity="0.5"/>
  </svg>
);

// Stoneware - Dense ceramic with speckled glaze
export const StonewareIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#708090" rx="2"/>
    <circle cx="3" cy="3" r="0.4" fill="#A9A9A9"/>
    <circle cx="7" cy="2" r="0.3" fill="#D3D3D3"/>
    <circle cx="11" cy="4" r="0.5" fill="#A9A9A9"/>
    <circle cx="15" cy="3" r="0.2" fill="#D3D3D3"/>
    <circle cx="19" cy="5" r="0.4" fill="#A9A9A9"/>
    <circle cx="21" cy="2" r="0.3" fill="#D3D3D3"/>
    <circle cx="2" cy="8" r="0.3" fill="#D3D3D3"/>
    <circle cx="6" cy="7" r="0.5" fill="#A9A9A9"/>
    <circle cx="10" cy="9" r="0.2" fill="#D3D3D3"/>
    <circle cx="14" cy="8" r="0.4" fill="#A9A9A9"/>
    <circle cx="18" cy="10" r="0.3" fill="#D3D3D3"/>
    <circle cx="22" cy="9" r="0.5" fill="#A9A9A9"/>
    <circle cx="4" cy="13" r="0.4" fill="#A9A9A9"/>
    <circle cx="8" cy="14" r="0.2" fill="#D3D3D3"/>
    <circle cx="12" cy="12" r="0.5" fill="#A9A9A9"/>
    <circle cx="16" cy="15" r="0.3" fill="#D3D3D3"/>
    <circle cx="20" cy="14" r="0.4" fill="#A9A9A9"/>
    <circle cx="1" cy="18" r="0.3" fill="#D3D3D3"/>
    <circle cx="5" cy="19" r="0.5" fill="#A9A9A9"/>
    <circle cx="9" cy="17" r="0.2" fill="#D3D3D3"/>
    <circle cx="13" cy="20" r="0.4" fill="#A9A9A9"/>
    <circle cx="17" cy="18" r="0.3" fill="#D3D3D3"/>
    <circle cx="21" cy="19" r="0.5" fill="#A9A9A9"/>
    <circle cx="3" cy="22" r="0.2" fill="#D3D3D3"/>
    <circle cx="7" cy="23" r="0.4" fill="#A9A9A9"/>
    <circle cx="11" cy="21" r="0.3" fill="#D3D3D3"/>
    <circle cx="15" cy="23" r="0.5" fill="#A9A9A9"/>
    <circle cx="19" cy="22" r="0.2" fill="#D3D3D3"/>
    <ellipse cx="12" cy="12" rx="6" ry="4" fill="#FFFFFF" opacity="0.2"/>
  </svg>
);

// Earthenware - Rustic pottery with rough texture
export const EarthenwareIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <filter id="earthenTexture">
        <feTurbulence baseFrequency="0.7" numOctaves="3" result="noise"/>
        <feDisplacementMap in="SourceGraphic" in2="noise" scale="1.2"/>
      </filter>
    </defs>
    <rect width="24" height="24" fill="#8B4513" rx="2"/>
    <rect width="24" height="24" fill="#A0522D" opacity="0.8" rx="2" filter="url(#earthenTexture)"/>
    <path d="M0,6 Q6,4 12,6 Q18,8 24,6 Q20,12 12,10 Q4,8 0,12" fill="#CD853F" opacity="0.7"/>
    <path d="M0,18 Q6,16 12,18 Q18,20 24,18 Q20,24 12,22 Q4,20 0,24" fill="#DEB887" opacity="0.6"/>
    <circle cx="6" cy="8" r="1.2" fill="#654321" opacity="0.8"/>
    <circle cx="18" cy="14" r="1" fill="#654321" opacity="0.7"/>
    <circle cx="10" cy="20" r="0.8" fill="#654321" opacity="0.6"/>
    <circle cx="20" cy="4" r="0.6" fill="#654321" opacity="0.9"/>
    <ellipse cx="4" cy="16" rx="1.5" ry="1" fill="#DEB887" opacity="0.5"/>
    <ellipse cx="16" cy="6" rx="1" ry="1.5" fill="#DEB887" opacity="0.6"/>
    <ellipse cx="12" cy="12" rx="2" ry="1.2" fill="#F4A460" opacity="0.4"/>
  </svg>
);

// Glazed Ceramic - Shiny glossy finish
export const GlazedCeramicIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="glazeGrad" cx="30%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="30%" stopColor="#E0FFFF"/>
        <stop offset="70%" stopColor="#B0E0E6"/>
        <stop offset="100%" stopColor="#87CEEB"/>
      </radialGradient>
      <linearGradient id="glazeShine" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.9"/>
        <stop offset="50%" stopColor="transparent"/>
        <stop offset="100%" stopColor="#000000" stopOpacity="0.1"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#glazeGrad)" rx="2"/>
    <rect width="24" height="24" fill="url(#glazeShine)" rx="2"/>
    <ellipse cx="8" cy="6" rx="4" ry="2" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="16" cy="18" rx="3" ry="1.5" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="18" cy="8" r="2" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="6" cy="16" r="1.5" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="12" cy="12" r="1" fill="#FFFFFF" opacity="0.95"/>
    <path d="M2,2 L8,8 M16,2 L22,8 M2,16 L8,22 M16,16 L22,22" stroke="#FFFFFF" strokeWidth="0.5" opacity="0.6"/>
    <ellipse cx="20" cy="4" rx="1.5" ry="1" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="4" cy="20" rx="1" ry="1.5" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Ceramic Icon Mapper
export const getUniqueCeramicIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Porcelain': return <PorcelainIcon {...iconProps} />;
    case 'Ceramic Tile': return <CeramicTileIcon {...iconProps} />;
    case 'Terra Cotta': return <TerraCottaIcon {...iconProps} />;
    case 'Stoneware': return <StonewareIcon {...iconProps} />;
    case 'Earthenware': return <EarthenwareIcon {...iconProps} />;
    case 'Glazed Ceramic': return <GlazedCeramicIcon {...iconProps} />;
    default: return <PorcelainIcon {...iconProps} />;
  }
};
