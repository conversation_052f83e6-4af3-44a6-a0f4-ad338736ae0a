import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Quartz Composite - Engineered quartz with sparkles
export const QuartzCompositeIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="quartzCompGrad" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="50%" stopColor="#F8F8FF"/>
        <stop offset="100%" stopColor="#E6E6FA"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#quartzCompGrad)" rx="2"/>
    <polygon points="4,2 6,4 4,6 2,4" fill="#FFD700" opacity="0.8"/>
    <polygon points="8,1 10,3 8,5 6,3" fill="#FFFFFF" opacity="0.9"/>
    <polygon points="12,3 14,5 12,7 10,5" fill="#FFD700" opacity="0.7"/>
    <polygon points="16,2 18,4 16,6 14,4" fill="#FFFFFF" opacity="0.8"/>
    <polygon points="20,4 22,6 20,8 18,6" fill="#FFD700" opacity="0.9"/>
    <polygon points="2,8 4,10 2,12 0,10" fill="#FFFFFF" opacity="0.8"/>
    <polygon points="6,7 8,9 6,11 4,9" fill="#FFD700" opacity="0.6"/>
    <polygon points="10,9 12,11 10,13 8,11" fill="#FFFFFF" opacity="0.9"/>
    <polygon points="14,8 16,10 14,12 12,10" fill="#FFD700" opacity="0.8"/>
    <polygon points="18,10 20,12 18,14 16,12" fill="#FFFFFF" opacity="0.7"/>
    <polygon points="22,9 24,11 22,13 20,11" fill="#FFD700" opacity="0.9"/>
    <polygon points="3,14 5,16 3,18 1,16" fill="#FFD700" opacity="0.7"/>
    <polygon points="7,13 9,15 7,17 5,15" fill="#FFFFFF" opacity="0.8"/>
    <polygon points="11,15 13,17 11,19 9,17" fill="#FFD700" opacity="0.9"/>
    <polygon points="15,14 17,16 15,18 13,16" fill="#FFFFFF" opacity="0.6"/>
    <polygon points="19,16 21,18 19,20 17,18" fill="#FFD700" opacity="0.8"/>
    <polygon points="23,15 24,16 24,18 23,19 21,17" fill="#FFFFFF" opacity="0.7"/>
    <polygon points="4,20 6,22 4,24 2,22" fill="#FFFFFF" opacity="0.9"/>
    <polygon points="8,19 10,21 8,23 6,21" fill="#FFD700" opacity="0.8"/>
    <polygon points="12,21 14,23 12,24 10,23" fill="#FFFFFF" opacity="0.7"/>
    <polygon points="16,20 18,22 16,24 14,22" fill="#FFD700" opacity="0.9"/>
    <polygon points="20,22 22,24 20,24 18,24" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="5" cy="5" r="0.3" fill="#C0C0C0" opacity="0.8"/>
    <circle cx="11" cy="8" r="0.2" fill="#C0C0C0" opacity="0.7"/>
    <circle cx="17" cy="11" r="0.4" fill="#C0C0C0" opacity="0.9"/>
    <circle cx="9" cy="18" r="0.3" fill="#C0C0C0" opacity="0.6"/>
    <circle cx="21" cy="21" r="0.2" fill="#C0C0C0" opacity="0.8"/>
  </svg>
);

// Engineered Stone - Layered composite structure
export const EngineeredStoneIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#D3D3D3" rx="2"/>
    <rect x="0" y="0" width="24" height="4" fill="#A9A9A9" opacity="0.8"/>
    <rect x="0" y="4" width="24" height="4" fill="#C0C0C0" opacity="0.9"/>
    <rect x="0" y="8" width="24" height="4" fill="#A9A9A9" opacity="0.8"/>
    <rect x="0" y="12" width="24" height="4" fill="#C0C0C0" opacity="0.9"/>
    <rect x="0" y="16" width="24" height="4" fill="#A9A9A9" opacity="0.8"/>
    <rect x="0" y="20" width="24" height="4" fill="#C0C0C0" opacity="0.9"/>
    <circle cx="3" cy="2" r="0.5" fill="#2F2F2F"/>
    <circle cx="7" cy="1" r="0.3" fill="#FFFFFF"/>
    <circle cx="11" cy="3" r="0.4" fill="#2F2F2F"/>
    <circle cx="15" cy="2" r="0.6" fill="#FFFFFF"/>
    <circle cx="19" cy="1" r="0.3" fill="#2F2F2F"/>
    <circle cx="21" cy="3" r="0.4" fill="#FFFFFF"/>
    <circle cx="2" cy="6" r="0.4" fill="#FFFFFF"/>
    <circle cx="6" cy="5" r="0.5" fill="#2F2F2F"/>
    <circle cx="10" cy="7" r="0.3" fill="#FFFFFF"/>
    <circle cx="14" cy="6" r="0.6" fill="#2F2F2F"/>
    <circle cx="18" cy="5" r="0.4" fill="#FFFFFF"/>
    <circle cx="22" cy="7" r="0.3" fill="#2F2F2F"/>
    <circle cx="4" cy="10" r="0.6" fill="#2F2F2F"/>
    <circle cx="8" cy="9" r="0.3" fill="#FFFFFF"/>
    <circle cx="12" cy="11" r="0.5" fill="#2F2F2F"/>
    <circle cx="16" cy="10" r="0.4" fill="#FFFFFF"/>
    <circle cx="20" cy="9" r="0.6" fill="#2F2F2F"/>
    <circle cx="1" cy="14" r="0.3" fill="#FFFFFF"/>
    <circle cx="5" cy="13" r="0.5" fill="#2F2F2F"/>
    <circle cx="9" cy="15" r="0.4" fill="#FFFFFF"/>
    <circle cx="13" cy="14" r="0.3" fill="#2F2F2F"/>
    <circle cx="17" cy="13" r="0.6" fill="#FFFFFF"/>
    <circle cx="21" cy="15" r="0.4" fill="#2F2F2F"/>
    <circle cx="3" cy="18" r="0.4" fill="#FFFFFF"/>
    <circle cx="7" cy="17" r="0.6" fill="#2F2F2F"/>
    <circle cx="11" cy="19" r="0.3" fill="#FFFFFF"/>
    <circle cx="15" cy="18" r="0.5" fill="#2F2F2F"/>
    <circle cx="19" cy="17" r="0.4" fill="#FFFFFF"/>
    <circle cx="23" cy="19" r="0.3" fill="#2F2F2F"/>
    <circle cx="2" cy="22" r="0.5" fill="#2F2F2F"/>
    <circle cx="6" cy="21" r="0.3" fill="#FFFFFF"/>
    <circle cx="10" cy="23" r="0.4" fill="#2F2F2F"/>
    <circle cx="14" cy="22" r="0.6" fill="#FFFFFF"/>
    <circle cx="18" cy="21" r="0.3" fill="#2F2F2F"/>
    <circle cx="22" cy="23" r="0.4" fill="#FFFFFF"/>
  </svg>
);

// Concrete - Aggregate with cement matrix
export const ConcreteIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#808080" rx="2"/>
    <circle cx="3" cy="3" r="1.2" fill="#A9A9A9"/>
    <circle cx="7" cy="2" r="0.8" fill="#D3D3D3"/>
    <circle cx="11" cy="4" r="1.5" fill="#A9A9A9"/>
    <circle cx="15" cy="3" r="1" fill="#D3D3D3"/>
    <circle cx="19" cy="5" r="1.3" fill="#A9A9A9"/>
    <circle cx="21" cy="2" r="0.7" fill="#D3D3D3"/>
    <circle cx="2" cy="8" r="1" fill="#D3D3D3"/>
    <circle cx="6" cy="7" r="1.4" fill="#A9A9A9"/>
    <circle cx="10" cy="9" r="0.9" fill="#D3D3D3"/>
    <circle cx="14" cy="8" r="1.2" fill="#A9A9A9"/>
    <circle cx="18" cy="10" r="1.1" fill="#D3D3D3"/>
    <circle cx="22" cy="9" r="1.5" fill="#A9A9A9"/>
    <circle cx="4" cy="13" r="1.3" fill="#A9A9A9"/>
    <circle cx="8" cy="14" r="0.8" fill="#D3D3D3"/>
    <circle cx="12" cy="12" r="1.6" fill="#A9A9A9"/>
    <circle cx="16" cy="15" r="1" fill="#D3D3D3"/>
    <circle cx="20" cy="14" r="1.2" fill="#A9A9A9"/>
    <circle cx="1" cy="18" r="1.1" fill="#D3D3D3"/>
    <circle cx="5" cy="19" r="1.4" fill="#A9A9A9"/>
    <circle cx="9" cy="17" r="0.9" fill="#D3D3D3"/>
    <circle cx="13" cy="20" r="1.3" fill="#A9A9A9"/>
    <circle cx="17" cy="18" r="1" fill="#D3D3D3"/>
    <circle cx="21" cy="19" r="1.5" fill="#A9A9A9"/>
    <circle cx="3" cy="22" r="0.8" fill="#D3D3D3"/>
    <circle cx="7" cy="23" r="1.2" fill="#A9A9A9"/>
    <circle cx="11" cy="21" r="1" fill="#D3D3D3"/>
    <circle cx="15" cy="23" r="1.4" fill="#A9A9A9"/>
    <circle cx="19" cy="22" r="0.9" fill="#D3D3D3"/>
    <ellipse cx="5" cy="6" rx="0.5" ry="0.3" fill="#2F2F2F" opacity="0.6"/>
    <ellipse cx="13" cy="11" rx="0.4" ry="0.6" fill="#2F2F2F" opacity="0.7"/>
    <ellipse cx="18" cy="16" rx="0.6" ry="0.4" fill="#2F2F2F" opacity="0.5"/>
    <ellipse cx="9" cy="20" rx="0.3" ry="0.5" fill="#2F2F2F" opacity="0.8"/>
  </svg>
);

// Terrazzo - Marble chips in cement matrix
export const TerrazzoIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5F5F5" rx="2"/>
    <polygon points="3,2 5,3 4,5 2,4" fill="#FF6B6B" opacity="0.8"/>
    <polygon points="7,1 9,2 8,4 6,3" fill="#4ECDC4" opacity="0.9"/>
    <polygon points="11,3 13,4 12,6 10,5" fill="#45B7D1" opacity="0.7"/>
    <polygon points="15,2 17,3 16,5 14,4" fill="#96CEB4" opacity="0.8"/>
    <polygon points="19,4 21,5 20,7 18,6" fill="#FFEAA7" opacity="0.9"/>
    <polygon points="22,1 24,2 23,4 21,3" fill="#FF6B6B" opacity="0.6"/>
    <polygon points="1,7 3,8 2,10 0,9" fill="#4ECDC4" opacity="0.8"/>
    <polygon points="5,6 7,7 6,9 4,8" fill="#45B7D1" opacity="0.9"/>
    <polygon points="9,8 11,9 10,11 8,10" fill="#96CEB4" opacity="0.7"/>
    <polygon points="13,7 15,8 14,10 12,9" fill="#FFEAA7" opacity="0.8"/>
    <polygon points="17,9 19,10 18,12 16,11" fill="#FF6B6B" opacity="0.9"/>
    <polygon points="21,8 23,9 22,11 20,10" fill="#4ECDC4" opacity="0.6"/>
    <polygon points="2,13 4,14 3,16 1,15" fill="#45B7D1" opacity="0.8"/>
    <polygon points="6,12 8,13 7,15 5,14" fill="#96CEB4" opacity="0.9"/>
    <polygon points="10,14 12,15 11,17 9,16" fill="#FFEAA7" opacity="0.7"/>
    <polygon points="14,13 16,14 15,16 13,15" fill="#FF6B6B" opacity="0.8"/>
    <polygon points="18,15 20,16 19,18 17,17" fill="#4ECDC4" opacity="0.9"/>
    <polygon points="22,14 24,15 23,17 21,16" fill="#45B7D1" opacity="0.6"/>
    <polygon points="3,19 5,20 4,22 2,21" fill="#96CEB4" opacity="0.8"/>
    <polygon points="7,18 9,19 8,21 6,20" fill="#FFEAA7" opacity="0.9"/>
    <polygon points="11,20 13,21 12,23 10,22" fill="#FF6B6B" opacity="0.7"/>
    <polygon points="15,19 17,20 16,22 14,21" fill="#4ECDC4" opacity="0.8"/>
    <polygon points="19,21 21,22 20,24 18,23" fill="#45B7D1" opacity="0.9"/>
    <polygon points="23,20 24,21 24,23 23,24 21,23" fill="#96CEB4" opacity="0.6"/>
    <polygon points="1,24 3,24 2,22 0,23" fill="#FFEAA7" opacity="0.8"/>
    <polygon points="5,23 7,24 6,22 4,23" fill="#FF6B6B" opacity="0.9"/>
    <polygon points="9,24 11,24 10,22 8,23" fill="#4ECDC4" opacity="0.7"/>
    <polygon points="13,23 15,24 14,22 12,23" fill="#45B7D1" opacity="0.8"/>
    <polygon points="17,24 19,24 18,22 16,23" fill="#96CEB4" opacity="0.9"/>
    <circle cx="4" cy="4" r="0.3" fill="#2F2F2F" opacity="0.5"/>
    <circle cx="12" cy="8" r="0.2" fill="#2F2F2F" opacity="0.6"/>
    <circle cx="20" cy="12" r="0.4" fill="#2F2F2F" opacity="0.4"/>
    <circle cx="8" cy="16" r="0.3" fill="#2F2F2F" opacity="0.7"/>
    <circle cx="16" cy="20" r="0.2" fill="#2F2F2F" opacity="0.5"/>
  </svg>
);

// Corian - Solid surface with uniform appearance
export const CorianIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="corianGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="30%" stopColor="#F8F8FF"/>
        <stop offset="70%" stopColor="#F0F0F0"/>
        <stop offset="100%" stopColor="#E8E8E8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#corianGrad)" rx="2"/>
    <circle cx="4" cy="4" r="0.8" fill="#E0E0E0" opacity="0.6"/>
    <circle cx="8" cy="3" r="0.6" fill="#D8D8D8" opacity="0.7"/>
    <circle cx="12" cy="5" r="0.9" fill="#E0E0E0" opacity="0.5"/>
    <circle cx="16" cy="4" r="0.7" fill="#D8D8D8" opacity="0.8"/>
    <circle cx="20" cy="6" r="0.5" fill="#E0E0E0" opacity="0.6"/>
    <circle cx="2" cy="8" r="0.7" fill="#D8D8D8" opacity="0.7"/>
    <circle cx="6" cy="7" r="0.9" fill="#E0E0E0" opacity="0.6"/>
    <circle cx="10" cy="9" r="0.5" fill="#D8D8D8" opacity="0.8"/>
    <circle cx="14" cy="8" r="0.8" fill="#E0E0E0" opacity="0.5"/>
    <circle cx="18" cy="10" r="0.6" fill="#D8D8D8" opacity="0.7"/>
    <circle cx="22" cy="9" r="0.7" fill="#E0E0E0" opacity="0.6"/>
    <circle cx="3" cy="12" r="0.6" fill="#E0E0E0" opacity="0.8"/>
    <circle cx="7" cy="11" r="0.8" fill="#D8D8D8" opacity="0.5"/>
    <circle cx="11" cy="13" r="0.7" fill="#E0E0E0" opacity="0.7"/>
    <circle cx="15" cy="12" r="0.5" fill="#D8D8D8" opacity="0.6"/>
    <circle cx="19" cy="14" r="0.9" fill="#E0E0E0" opacity="0.8"/>
    <circle cx="23" cy="13" r="0.6" fill="#D8D8D8" opacity="0.7"/>
    <circle cx="1" cy="16" r="0.8" fill="#D8D8D8" opacity="0.6"/>
    <circle cx="5" cy="15" r="0.5" fill="#E0E0E0" opacity="0.9"/>
    <circle cx="9" cy="17" r="0.7" fill="#D8D8D8" opacity="0.5"/>
    <circle cx="13" cy="16" r="0.9" fill="#E0E0E0" opacity="0.7"/>
    <circle cx="17" cy="18" r="0.6" fill="#D8D8D8" opacity="0.8"/>
    <circle cx="21" cy="17" r="0.8" fill="#E0E0E0" opacity="0.6"/>
    <circle cx="4" cy="20" r="0.7" fill="#E0E0E0" opacity="0.7"/>
    <circle cx="8" cy="19" r="0.9" fill="#D8D8D8" opacity="0.6"/>
    <circle cx="12" cy="21" r="0.5" fill="#E0E0E0" opacity="0.8"/>
    <circle cx="16" cy="20" r="0.8" fill="#D8D8D8" opacity="0.5"/>
    <circle cx="20" cy="22" r="0.6" fill="#E0E0E0" opacity="0.7"/>
    <circle cx="2" cy="23" r="0.7" fill="#D8D8D8" opacity="0.8"/>
    <circle cx="6" cy="22" r="0.5" fill="#E0E0E0" opacity="0.6"/>
    <circle cx="10" cy="24" r="0.8" fill="#D8D8D8" opacity="0.7"/>
    <circle cx="14" cy="23" r="0.6" fill="#E0E0E0" opacity="0.9"/>
    <circle cx="18" cy="24" r="0.7" fill="#D8D8D8" opacity="0.5"/>
    <circle cx="22" cy="23" r="0.9" fill="#E0E0E0" opacity="0.8"/>
  </svg>
);

// Recycled Glass - Crushed glass aggregate
export const RecycledGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#E6F3FF" rx="2"/>
    <polygon points="3,2 5,3 4,5 2,4" fill="#4ECDC4" opacity="0.8"/>
    <polygon points="7,1 9,2 8,4 6,3" fill="#45B7D1" opacity="0.9"/>
    <polygon points="11,3 13,4 12,6 10,5" fill="#96CEB4" opacity="0.7"/>
    <polygon points="15,2 17,3 16,5 14,4" fill="#4ECDC4" opacity="0.8"/>
    <polygon points="19,4 21,5 20,7 18,6" fill="#45B7D1" opacity="0.9"/>
    <polygon points="22,1 24,2 23,4 21,3" fill="#96CEB4" opacity="0.6"/>
    <polygon points="1,7 3,8 2,10 0,9" fill="#45B7D1" opacity="0.8"/>
    <polygon points="5,6 7,7 6,9 4,8" fill="#96CEB4" opacity="0.9"/>
    <polygon points="9,8 11,9 10,11 8,10" fill="#4ECDC4" opacity="0.7"/>
    <polygon points="13,7 15,8 14,10 12,9" fill="#45B7D1" opacity="0.8"/>
    <polygon points="17,9 19,10 18,12 16,11" fill="#96CEB4" opacity="0.9"/>
    <polygon points="21,8 23,9 22,11 20,10" fill="#4ECDC4" opacity="0.6"/>
    <polygon points="2,13 4,14 3,16 1,15" fill="#96CEB4" opacity="0.8"/>
    <polygon points="6,12 8,13 7,15 5,14" fill="#4ECDC4" opacity="0.9"/>
    <polygon points="10,14 12,15 11,17 9,16" fill="#45B7D1" opacity="0.7"/>
    <polygon points="14,13 16,14 15,16 13,15" fill="#96CEB4" opacity="0.8"/>
    <polygon points="18,15 20,16 19,18 17,17" fill="#4ECDC4" opacity="0.9"/>
    <polygon points="22,14 24,15 23,17 21,16" fill="#45B7D1" opacity="0.6"/>
    <polygon points="3,19 5,20 4,22 2,21" fill="#4ECDC4" opacity="0.8"/>
    <polygon points="7,18 9,19 8,21 6,20" fill="#45B7D1" opacity="0.9"/>
    <polygon points="11,20 13,21 12,23 10,22" fill="#96CEB4" opacity="0.7"/>
    <polygon points="15,19 17,20 16,22 14,21" fill="#4ECDC4" opacity="0.8"/>
    <polygon points="19,21 21,22 20,24 18,23" fill="#45B7D1" opacity="0.9"/>
    <polygon points="23,20 24,21 24,23 23,24 21,23" fill="#96CEB4" opacity="0.6"/>
    <polygon points="1,24 3,24 2,22 0,23" fill="#45B7D1" opacity="0.8"/>
    <polygon points="5,23 7,24 6,22 4,23" fill="#96CEB4" opacity="0.9"/>
    <polygon points="9,24 11,24 10,22 8,23" fill="#4ECDC4" opacity="0.7"/>
    <polygon points="13,23 15,24 14,22 12,23" fill="#45B7D1" opacity="0.8"/>
    <polygon points="17,24 19,24 18,22 16,23" fill="#96CEB4" opacity="0.9"/>
    <circle cx="4" cy="4" r="0.5" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="12" cy="8" r="0.4" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="20" cy="12" r="0.6" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="8" cy="16" r="0.3" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="16" cy="20" r="0.5" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="6" cy="10" rx="0.8" ry="0.5" fill="#FFFFFF" opacity="0.6"/>
    <ellipse cx="14" cy="6" rx="0.6" ry="0.8" fill="#FFFFFF" opacity="0.7"/>
    <ellipse cx="18" cy="18" rx="0.7" ry="0.4" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="10" cy="22" rx="0.5" ry="0.7" fill="#FFFFFF" opacity="0.6"/>
  </svg>
);

// Composite Icon Mapper
export const getUniqueCompositeIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Quartz Composite': return <QuartzCompositeIcon {...iconProps} />;
    case 'Engineered Stone': return <EngineeredStoneIcon {...iconProps} />;
    case 'Concrete': return <ConcreteIcon {...iconProps} />;
    case 'Terrazzo': return <TerrazzoIcon {...iconProps} />;
    case 'Corian': return <CorianIcon {...iconProps} />;
    case 'Recycled Glass': return <RecycledGlassIcon {...iconProps} />;
    default: return <QuartzCompositeIcon {...iconProps} />;
  }
};
