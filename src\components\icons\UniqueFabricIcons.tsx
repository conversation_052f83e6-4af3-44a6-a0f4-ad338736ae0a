import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Linen - Loose natural weave with irregular threads
export const LinenIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5F5DC" rx="2"/>
    <path d="M0,3 L24,3 M0,6 L24,6 M0,9 L24,9 M0,12 L24,12 M0,15 L24,15 M0,18 L24,18 M0,21 L24,21" 
          stroke="#E6E6FA" strokeWidth="0.8" opacity="0.7"/>
    <path d="M3,0 L3,24 M6,0 L6,24 M9,0 L9,24 M12,0 L12,24 M15,0 L15,24 M18,0 L18,24 M21,0 L21,24" 
          stroke="#E6E6FA" strokeWidth="0.8" opacity="0.7"/>
    <path d="M0,4.5 Q6,3.5 12,4.5 Q18,5.5 24,4.5" stroke="#DDD" strokeWidth="0.4" fill="none"/>
    <path d="M0,10.5 Q6,9.5 12,10.5 Q18,11.5 24,10.5" stroke="#DDD" strokeWidth="0.4" fill="none"/>
    <path d="M0,16.5 Q6,15.5 12,16.5 Q18,17.5 24,16.5" stroke="#DDD" strokeWidth="0.4" fill="none"/>
    <path d="M4.5,0 Q3.5,6 4.5,12 Q5.5,18 4.5,24" stroke="#DDD" strokeWidth="0.4" fill="none"/>
    <path d="M13.5,0 Q12.5,6 13.5,12 Q14.5,18 13.5,24" stroke="#DDD" strokeWidth="0.4" fill="none"/>
  </svg>
);

// Cotton - Fluffy cotton ball texture
export const CottonIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#FFFAF0" rx="2"/>
    <circle cx="6" cy="4" r="2.5" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="12" cy="3" r="2" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="18" cy="5" r="2.2" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="4" cy="8" r="1.8" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="10" cy="7" r="2.3" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="16" cy="9" r="1.9" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="20" cy="8" r="1.5" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="2" cy="12" r="1.6" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="8" cy="11" r="2.1" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="14" cy="13" r="2.4" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="22" cy="12" r="1.7" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="5" cy="16" r="2" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="11" cy="15" r="1.8" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="17" cy="17" r="2.2" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="3" cy="20" r="1.9" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="9" cy="19" r="2.3" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="15" cy="21" r="1.6" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="21" cy="20" r="1.8" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Velvet - Directional pile with rich depth
export const VelvetIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="velvetGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#8B008B"/>
        <stop offset="30%" stopColor="#9932CC"/>
        <stop offset="70%" stopColor="#8B008B"/>
        <stop offset="100%" stopColor="#4B0082"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#velvetGrad)" rx="2"/>
    <rect x="0" y="0" width="6" height="24" fill="#9932CC" opacity="0.6"/>
    <rect x="6" y="0" width="6" height="24" fill="#8B008B" opacity="0.4"/>
    <rect x="12" y="0" width="6" height="24" fill="#9932CC" opacity="0.7"/>
    <rect x="18" y="0" width="6" height="24" fill="#8B008B" opacity="0.5"/>
    <path d="M0,4 Q6,2 12,4 Q18,6 24,4" stroke="#DDA0DD" strokeWidth="0.8" opacity="0.6"/>
    <path d="M0,8 Q6,6 12,8 Q18,10 24,8" stroke="#DDA0DD" strokeWidth="0.8" opacity="0.5"/>
    <path d="M0,12 Q6,10 12,12 Q18,14 24,12" stroke="#DDA0DD" strokeWidth="0.8" opacity="0.6"/>
    <path d="M0,16 Q6,14 12,16 Q18,18 24,16" stroke="#DDA0DD" strokeWidth="0.8" opacity="0.5"/>
    <path d="M0,20 Q6,18 12,20 Q18,22 24,20" stroke="#DDA0DD" strokeWidth="0.8" opacity="0.6"/>
  </svg>
);

// Silk - Smooth lustrous sheen with flowing drape
export const SilkIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="silkGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFF8DC"/>
        <stop offset="30%" stopColor="#FFFACD"/>
        <stop offset="70%" stopColor="#F0E68C"/>
        <stop offset="100%" stopColor="#DDD26A"/>
      </linearGradient>
      <linearGradient id="silkSheen" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stopColor="transparent"/>
        <stop offset="30%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="70%" stopColor="#FFFFFF" stopOpacity="0.8"/>
        <stop offset="100%" stopColor="transparent"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#silkGrad)" rx="2"/>
    <path d="M0,0 Q6,4 12,0 Q18,4 24,0 L24,8 Q18,12 12,8 Q6,4 0,8 Z" fill="url(#silkSheen)"/>
    <path d="M0,8 Q6,12 12,8 Q18,12 24,8 L24,16 Q18,20 12,16 Q6,12 0,16 Z" fill="url(#silkSheen)" opacity="0.7"/>
    <path d="M0,16 Q6,20 12,16 Q18,20 24,16 L24,24 Q18,24 12,24 Q6,24 0,24 Z" fill="url(#silkSheen)" opacity="0.5"/>
    <ellipse cx="8" cy="6" rx="2" ry="1" fill="#FFFFFF" opacity="0.9"/>
    <ellipse cx="16" cy="14" rx="1.5" ry="0.8" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="20" cy="22" rx="1" ry="0.6" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Wool - Fuzzy knitted texture
export const WoolIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5DEB3" rx="2"/>
    <path d="M0,3 Q3,1 6,3 Q9,5 12,3 Q15,1 18,3 Q21,5 24,3 L24,6 Q21,8 18,6 Q15,4 12,6 Q9,8 6,6 Q3,4 0,6 Z" 
          fill="#DEB887" opacity="0.8"/>
    <path d="M0,9 Q3,7 6,9 Q9,11 12,9 Q15,7 18,9 Q21,11 24,9 L24,12 Q21,14 18,12 Q15,10 12,12 Q9,14 6,12 Q3,10 0,12 Z" 
          fill="#D2B48C" opacity="0.7"/>
    <path d="M0,15 Q3,13 6,15 Q9,17 12,15 Q15,13 18,15 Q21,17 24,15 L24,18 Q21,20 18,18 Q15,16 12,18 Q9,20 6,18 Q3,16 0,18 Z" 
          fill="#DEB887" opacity="0.8"/>
    <path d="M0,21 Q3,19 6,21 Q9,23 12,21 Q15,19 18,21 Q21,23 24,21 L24,24 L0,24 Z" 
          fill="#D2B48C" opacity="0.7"/>
    <circle cx="4" cy="4" r="0.5" fill="#CD853F" opacity="0.6"/>
    <circle cx="8" cy="7" r="0.4" fill="#CD853F" opacity="0.5"/>
    <circle cx="12" cy="10" r="0.6" fill="#CD853F" opacity="0.7"/>
    <circle cx="16" cy="13" r="0.3" fill="#CD853F" opacity="0.4"/>
    <circle cx="20" cy="16" r="0.5" fill="#CD853F" opacity="0.6"/>
  </svg>
);

// Cashmere - Ultra-fine luxury fiber pattern
export const CashmereIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="cashmereGrad" cx="50%" cy="50%" r="50%">
        <stop offset="0%" stopColor="#F8F8FF"/>
        <stop offset="50%" stopColor="#E6E6FA"/>
        <stop offset="100%" stopColor="#DDA0DD"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#cashmereGrad)" rx="2"/>
    <path d="M0,0 L24,0 M0,1 L24,1 M0,2 L24,2 M0,3 L24,3 M0,4 L24,4 M0,5 L24,5 M0,6 L24,6 M0,7 L24,7 M0,8 L24,8 M0,9 L24,9 M0,10 L24,10 M0,11 L24,11 M0,12 L24,12 M0,13 L24,13 M0,14 L24,14 M0,15 L24,15 M0,16 L24,16 M0,17 L24,17 M0,18 L24,18 M0,19 L24,19 M0,20 L24,20 M0,21 L24,21 M0,22 L24,22 M0,23 L24,23 M0,24 L24,24" 
          stroke="#F0E68C" strokeWidth="0.1" opacity="0.3"/>
    <path d="M0,0 L0,24 M1,0 L1,24 M2,0 L2,24 M3,0 L3,24 M4,0 L4,24 M5,0 L5,24 M6,0 L6,24 M7,0 L7,24 M8,0 L8,24 M9,0 L9,24 M10,0 L10,24 M11,0 L11,24 M12,0 L12,24 M13,0 L13,24 M14,0 L14,24 M15,0 L15,24 M16,0 L16,24 M17,0 L17,24 M18,0 L18,24 M19,0 L19,24 M20,0 L20,24 M21,0 L21,24 M22,0 L22,24 M23,0 L23,24 M24,0 L24,24" 
          stroke="#F0E68C" strokeWidth="0.1" opacity="0.3"/>
    <ellipse cx="12" cy="12" rx="8" ry="6" fill="#FFFFFF" opacity="0.4"/>
    <ellipse cx="12" cy="12" rx="4" ry="3" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="12" cy="12" r="1" fill="#FFFFFF" opacity="0.8"/>
  </svg>
);

// Leather - Grain texture with natural patterns
export const LeatherIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <filter id="leatherTexture">
        <feTurbulence baseFrequency="0.8" numOctaves="4" result="noise"/>
        <feDisplacementMap in="SourceGraphic" in2="noise" scale="1"/>
      </filter>
    </defs>
    <rect width="24" height="24" fill="#8B4513" rx="2"/>
    <ellipse cx="4" cy="3" rx="1.5" ry="1" fill="#A0522D" opacity="0.7"/>
    <ellipse cx="8" cy="2" rx="1" ry="1.5" fill="#A0522D" opacity="0.6"/>
    <ellipse cx="12" cy="4" rx="1.8" ry="1.2" fill="#A0522D" opacity="0.8"/>
    <ellipse cx="16" cy="3" rx="1.2" ry="1.6" fill="#A0522D" opacity="0.5"/>
    <ellipse cx="20" cy="5" rx="1.6" ry="1" fill="#A0522D" opacity="0.7"/>
    <ellipse cx="2" cy="8" rx="1.3" ry="1.4" fill="#A0522D" opacity="0.6"/>
    <ellipse cx="6" cy="7" rx="1.7" ry="1.1" fill="#A0522D" opacity="0.8"/>
    <ellipse cx="10" cy="9" rx="1.1" ry="1.7" fill="#A0522D" opacity="0.5"/>
    <ellipse cx="14" cy="8" rx="1.5" ry="1.3" fill="#A0522D" opacity="0.7"/>
    <ellipse cx="18" cy="10" rx="1.4" ry="1" fill="#A0522D" opacity="0.6"/>
    <ellipse cx="22" cy="9" rx="1" ry="1.5" fill="#A0522D" opacity="0.8"/>
    <ellipse cx="3" cy="13" rx="1.6" ry="1.2" fill="#A0522D" opacity="0.7"/>
    <ellipse cx="7" cy="12" rx="1.2" ry="1.6" fill="#A0522D" opacity="0.5"/>
    <ellipse cx="11" cy="14" rx="1.8" ry="1.1" fill="#A0522D" opacity="0.8"/>
    <ellipse cx="15" cy="13" rx="1.1" ry="1.4" fill="#A0522D" opacity="0.6"/>
    <ellipse cx="19" cy="15" rx="1.5" ry="1.2" fill="#A0522D" opacity="0.7"/>
    <ellipse cx="1" cy="18" rx="1.4" ry="1.3" fill="#A0522D" opacity="0.6"/>
    <ellipse cx="5" cy="17" rx="1.7" ry="1" fill="#A0522D" opacity="0.8"/>
    <ellipse cx="9" cy="19" rx="1" ry="1.6" fill="#A0522D" opacity="0.5"/>
    <ellipse cx="13" cy="18" rx="1.6" ry="1.4" fill="#A0522D" opacity="0.7"/>
    <ellipse cx="17" cy="20" rx="1.3" ry="1.1" fill="#A0522D" opacity="0.6"/>
    <ellipse cx="21" cy="19" rx="1.1" ry="1.5" fill="#A0522D" opacity="0.8"/>
    <ellipse cx="4" cy="22" rx="1.5" ry="1.2" fill="#A0522D" opacity="0.7"/>
    <ellipse cx="8" cy="21" rx="1.2" ry="1.4" fill="#A0522D" opacity="0.5"/>
    <ellipse cx="12" cy="23" rx="1.8" ry="1" fill="#A0522D" opacity="0.8"/>
    <ellipse cx="16" cy="22" rx="1.1" ry="1.3" fill="#A0522D" opacity="0.6"/>
    <ellipse cx="20" cy="24" rx="1.4" ry="1.1" fill="#A0522D" opacity="0.7"/>
  </svg>
);

// Suede - Brushed nap texture
export const SuedeIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#D2B48C" rx="2"/>
    <path d="M0,0 L1,2 L0,4 L1,6 L0,8 L1,10 L0,12 L1,14 L0,16 L1,18 L0,20 L1,22 L0,24"
          stroke="#CD853F" strokeWidth="0.5" fill="none"/>
    <path d="M2,0 L3,2 L2,4 L3,6 L2,8 L3,10 L2,12 L3,14 L2,16 L3,18 L2,20 L3,22 L2,24"
          stroke="#DEB887" strokeWidth="0.5" fill="none"/>
    <path d="M4,0 L5,2 L4,4 L5,6 L4,8 L5,10 L4,12 L5,14 L4,16 L5,18 L4,20 L5,22 L4,24"
          stroke="#CD853F" strokeWidth="0.5" fill="none"/>
    <path d="M6,0 L7,2 L6,4 L7,6 L6,8 L7,10 L6,12 L7,14 L6,16 L7,18 L6,20 L7,22 L6,24"
          stroke="#DEB887" strokeWidth="0.5" fill="none"/>
    <path d="M8,0 L9,2 L8,4 L9,6 L8,8 L9,10 L8,12 L9,14 L8,16 L9,18 L8,20 L9,22 L8,24"
          stroke="#CD853F" strokeWidth="0.5" fill="none"/>
    <path d="M10,0 L11,2 L10,4 L11,6 L10,8 L11,10 L10,12 L11,14 L10,16 L11,18 L10,20 L11,22 L10,24"
          stroke="#DEB887" strokeWidth="0.5" fill="none"/>
    <path d="M12,0 L13,2 L12,4 L13,6 L12,8 L13,10 L12,12 L13,14 L12,16 L13,18 L12,20 L13,22 L12,24"
          stroke="#CD853F" strokeWidth="0.5" fill="none"/>
    <path d="M14,0 L15,2 L14,4 L15,6 L14,8 L15,10 L14,12 L15,14 L14,16 L15,18 L14,20 L15,22 L14,24"
          stroke="#DEB887" strokeWidth="0.5" fill="none"/>
    <path d="M16,0 L17,2 L16,4 L17,6 L16,8 L17,10 L16,12 L17,14 L16,16 L17,18 L16,20 L17,22 L16,24"
          stroke="#CD853F" strokeWidth="0.5" fill="none"/>
    <path d="M18,0 L19,2 L18,4 L19,6 L18,8 L19,10 L18,12 L19,14 L18,16 L19,18 L18,20 L19,22 L18,24"
          stroke="#DEB887" strokeWidth="0.5" fill="none"/>
    <path d="M20,0 L21,2 L20,4 L21,6 L20,8 L21,10 L20,12 L21,14 L20,16 L21,18 L20,20 L21,22 L20,24"
          stroke="#CD853F" strokeWidth="0.5" fill="none"/>
    <path d="M22,0 L23,2 L22,4 L23,6 L22,8 L23,10 L22,12 L23,14 L22,16 L23,18 L22,20 L23,22 L22,24"
          stroke="#DEB887" strokeWidth="0.5" fill="none"/>
  </svg>
);

// Chenille - Caterpillar-like fuzzy yarn texture
export const ChenilleIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DDA0DD" rx="2"/>
    <circle cx="2" cy="4" r="1.5" fill="#DA70D6" opacity="0.8"/>
    <circle cx="6" cy="4" r="1.2" fill="#FF69B4" opacity="0.7"/>
    <circle cx="10" cy="4" r="1.4" fill="#DA70D6" opacity="0.9"/>
    <circle cx="14" cy="4" r="1.1" fill="#FF69B4" opacity="0.6"/>
    <circle cx="18" cy="4" r="1.3" fill="#DA70D6" opacity="0.8"/>
    <circle cx="22" cy="4" r="1" fill="#FF69B4" opacity="0.7"/>
    <circle cx="2" cy="8" r="1.2" fill="#FF69B4" opacity="0.7"/>
    <circle cx="6" cy="8" r="1.5" fill="#DA70D6" opacity="0.8"/>
    <circle cx="10" cy="8" r="1.1" fill="#FF69B4" opacity="0.9"/>
    <circle cx="14" cy="8" r="1.4" fill="#DA70D6" opacity="0.6"/>
    <circle cx="18" cy="8" r="1" fill="#FF69B4" opacity="0.8"/>
    <circle cx="22" cy="8" r="1.3" fill="#DA70D6" opacity="0.7"/>
    <circle cx="2" cy="12" r="1.4" fill="#DA70D6" opacity="0.8"/>
    <circle cx="6" cy="12" r="1.1" fill="#FF69B4" opacity="0.7"/>
    <circle cx="10" cy="12" r="1.3" fill="#DA70D6" opacity="0.9"/>
    <circle cx="14" cy="12" r="1.2" fill="#FF69B4" opacity="0.6"/>
    <circle cx="18" cy="12" r="1.5" fill="#DA70D6" opacity="0.8"/>
    <circle cx="22" cy="12" r="1.1" fill="#FF69B4" opacity="0.7"/>
    <circle cx="2" cy="16" r="1.1" fill="#FF69B4" opacity="0.7"/>
    <circle cx="6" cy="16" r="1.4" fill="#DA70D6" opacity="0.8"/>
    <circle cx="10" cy="16" r="1.2" fill="#FF69B4" opacity="0.9"/>
    <circle cx="14" cy="16" r="1.5" fill="#DA70D6" opacity="0.6"/>
    <circle cx="18" cy="16" r="1" fill="#FF69B4" opacity="0.8"/>
    <circle cx="22" cy="16" r="1.3" fill="#DA70D6" opacity="0.7"/>
    <circle cx="2" cy="20" r="1.3" fill="#DA70D6" opacity="0.8"/>
    <circle cx="6" cy="20" r="1" fill="#FF69B4" opacity="0.7"/>
    <circle cx="10" cy="20" r="1.5" fill="#DA70D6" opacity="0.9"/>
    <circle cx="14" cy="20" r="1.1" fill="#FF69B4" opacity="0.6"/>
    <circle cx="18" cy="20" r="1.4" fill="#DA70D6" opacity="0.8"/>
    <circle cx="22" cy="20" r="1.2" fill="#FF69B4" opacity="0.7"/>
  </svg>
);

// Tweed - Herringbone pattern
export const TweedIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#8B7355" rx="2"/>
    <polygon points="0,0 6,0 3,6 0,6" fill="#A0522D"/>
    <polygon points="6,0 12,0 9,6 6,6" fill="#654321"/>
    <polygon points="12,0 18,0 15,6 12,6" fill="#A0522D"/>
    <polygon points="18,0 24,0 21,6 18,6" fill="#654321"/>
    <polygon points="3,6 9,6 6,12 0,12" fill="#654321"/>
    <polygon points="9,6 15,6 12,12 6,12" fill="#A0522D"/>
    <polygon points="15,6 21,6 18,12 12,12" fill="#654321"/>
    <polygon points="21,6 24,6 24,12 18,12" fill="#A0522D"/>
    <polygon points="0,12 6,12 3,18 0,18" fill="#A0522D"/>
    <polygon points="6,12 12,12 9,18 6,18" fill="#654321"/>
    <polygon points="12,12 18,12 15,18 12,18" fill="#A0522D"/>
    <polygon points="18,12 24,12 21,18 18,18" fill="#654321"/>
    <polygon points="3,18 9,18 6,24 0,24" fill="#654321"/>
    <polygon points="9,18 15,18 12,24 6,24" fill="#A0522D"/>
    <polygon points="15,18 21,18 18,24 12,24" fill="#654321"/>
    <polygon points="21,18 24,18 24,24 18,24" fill="#A0522D"/>
    <circle cx="4" cy="3" r="0.3" fill="#DEB887" opacity="0.6"/>
    <circle cx="10" cy="9" r="0.2" fill="#DEB887" opacity="0.5"/>
    <circle cx="16" cy="15" r="0.4" fill="#DEB887" opacity="0.7"/>
    <circle cx="22" cy="21" r="0.3" fill="#DEB887" opacity="0.4"/>
  </svg>
);

// Burlap - Coarse jute weave
export const BurlapIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DEB887" rx="2"/>
    <rect x="0" y="0" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="0" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="0" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="0" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="0" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="0" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="0" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="0" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="0" y="3" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="3" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="3" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="3" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="3" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="3" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="3" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="3" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="0" y="6" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="6" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="6" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="6" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="6" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="6" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="6" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="6" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="0" y="9" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="9" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="9" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="9" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="9" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="9" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="9" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="9" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="0" y="12" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="12" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="12" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="12" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="12" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="12" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="12" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="12" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="0" y="15" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="15" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="15" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="15" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="15" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="15" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="15" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="15" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="0" y="18" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="18" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="18" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="18" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="18" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="18" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="18" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="18" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="0" y="21" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="3" y="21" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="6" y="21" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="9" y="21" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="12" y="21" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="15" y="21" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="18" y="21" width="3" height="3" fill="#F4A460" stroke="#8B7355" strokeWidth="0.3"/>
    <rect x="21" y="21" width="3" height="3" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.3"/>
  </svg>
);

// Canvas - Heavy duty plain weave
export const CanvasIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5F5DC" rx="2"/>
    <path d="M0,0 L24,0 M0,4 L24,4 M0,8 L24,8 M0,12 L24,12 M0,16 L24,16 M0,20 L24,20 M0,24 L24,24"
          stroke="#DDD26A" strokeWidth="1.5" opacity="0.8"/>
    <path d="M0,0 L0,24 M4,0 L4,24 M8,0 L8,24 M12,0 L12,24 M16,0 L16,24 M20,0 L20,24 M24,0 L24,24"
          stroke="#DDD26A" strokeWidth="1.5" opacity="0.8"/>
    <rect x="0" y="0" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="8" y="0" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="16" y="0" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="4" y="4" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="12" y="4" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="20" y="4" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="0" y="8" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="8" y="8" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="16" y="8" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="4" y="12" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="12" y="12" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="20" y="12" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="0" y="16" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="8" y="16" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="16" y="16" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="4" y="20" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="12" y="20" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
    <rect x="20" y="20" width="4" height="4" fill="#F0E68C" opacity="0.6"/>
  </svg>
);

// Denim - Diagonal twill weave
export const DenimIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#4169E1" rx="2"/>
    <path d="M0,0 L6,6 M6,0 L12,6 M12,0 L18,6 M18,0 L24,6 M0,6 L6,12 M6,6 L12,12 M12,6 L18,12 M18,6 L24,12 M0,12 L6,18 M6,12 L12,18 M12,12 L18,18 M18,12 L24,18 M0,18 L6,24 M6,18 L12,24 M12,18 L18,24 M18,18 L24,24"
          stroke="#1E90FF" strokeWidth="1" opacity="0.7"/>
    <path d="M0,3 L3,6 M3,0 L9,6 M9,0 L15,6 M15,0 L21,6 M21,0 L24,3 M0,9 L3,12 M3,6 L9,12 M9,6 L15,12 M15,6 L21,12 M21,6 L24,9 M0,15 L3,18 M3,12 L9,18 M9,12 L15,18 M15,12 L21,18 M21,12 L24,15 M0,21 L3,24 M3,18 L9,24 M9,18 L15,24 M15,18 L21,24 M21,18 L24,21"
          stroke="#000080" strokeWidth="0.8" opacity="0.6"/>
    <circle cx="6" cy="6" r="0.5" fill="#FFD700" opacity="0.8"/>
    <circle cx="18" cy="18" r="0.4" fill="#FFD700" opacity="0.7"/>
    <rect x="10" y="2" width="0.5" height="3" fill="#FFD700" opacity="0.6"/>
    <rect x="14" y="19" width="0.5" height="3" fill="#FFD700" opacity="0.5"/>
  </svg>
);

// Faux Fur - Fluffy synthetic fur texture
export const FauxFurIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5F5DC" rx="2"/>
    <circle cx="2" cy="2" r="1.5" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="4" cy="1" r="1" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="6" cy="3" r="1.2" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="8" cy="2" r="0.8" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="10" cy="4" r="1.3" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="12" cy="1" r="1.1" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="14" cy="3" r="0.9" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="16" cy="2" r="1.4" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="18" cy="4" r="1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="20" cy="1" r="1.2" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="22" cy="3" r="0.8" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="1" cy="6" r="1.1" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="3" cy="5" r="1.3" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="5" cy="7" r="0.9" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="7" cy="6" r="1.5" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="9" cy="8" r="1" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="11" cy="5" r="1.2" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="13" cy="7" r="0.8" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="15" cy="6" r="1.4" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="17" cy="8" r="1.1" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="19" cy="5" r="0.9" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="21" cy="7" r="1.3" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="23" cy="6" r="1" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="2" cy="10" r="1.2" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="4" cy="9" r="0.8" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="6" cy="11" r="1.4" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="8" cy="10" r="1.1" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="10" cy="12" r="0.9" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="12" cy="9" r="1.3" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="14" cy="11" r="1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="16" cy="10" r="1.5" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="18" cy="12" r="0.8" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="20" cy="9" r="1.2" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="22" cy="11" r="1.1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="1" cy="14" r="0.9" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="3" cy="13" r="1.4" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="5" cy="15" r="1" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="7" cy="14" r="1.2" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="9" cy="16" r="0.8" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="11" cy="13" r="1.5" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="13" cy="15" r="1.1" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="15" cy="14" r="0.9" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="17" cy="16" r="1.3" fill="#F8F8FF" opacity="0.8"/>
    <circle cx="19" cy="13" r="1" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="21" cy="15" r="1.2" fill="#F8F8FF" opacity="0.9"/>
    <circle cx="23" cy="14" r="0.8" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="2" cy="18" r="1.1" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="4" cy="17" r="1.3" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="6" cy="19" r="0.9" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="8" cy="18" r="1.5" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="10" cy="20" r="1" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="12" cy="17" r="1.2" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="14" cy="19" r="0.8" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="16" cy="18" r="1.4" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="18" cy="20" r="1.1" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="20" cy="17" r="0.9" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="22" cy="19" r="1.3" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="1" cy="22" r="1" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="3" cy="21" r="1.2" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="5" cy="23" r="0.8" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="7" cy="22" r="1.4" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="9" cy="24" r="1.1" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="11" cy="21" r="0.9" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="13" cy="23" r="1.3" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="15" cy="22" r="1" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="17" cy="24" r="1.2" fill="#F8F8FF" opacity="0.6"/>
    <circle cx="19" cy="21" r="0.8" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="21" cy="23" r="1.5" fill="#F8F8FF" opacity="0.7"/>
    <circle cx="23" cy="22" r="1.1" fill="#FFFFFF" opacity="0.9"/>
  </svg>
);

// Fabric Icon Mapper
export const getUniqueFabricIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };

  switch (materialName) {
    case 'Linen': return <LinenIcon {...iconProps} />;
    case 'Cotton': return <CottonIcon {...iconProps} />;
    case 'Velvet': return <VelvetIcon {...iconProps} />;
    case 'Silk': return <SilkIcon {...iconProps} />;
    case 'Wool': return <WoolIcon {...iconProps} />;
    case 'Cashmere': return <CashmereIcon {...iconProps} />;
    case 'Leather': return <LeatherIcon {...iconProps} />;
    case 'Suede': return <SuedeIcon {...iconProps} />;
    case 'Chenille': return <ChenilleIcon {...iconProps} />;
    case 'Tweed': return <TweedIcon {...iconProps} />;
    case 'Burlap': return <BurlapIcon {...iconProps} />;
    case 'Canvas': return <CanvasIcon {...iconProps} />;
    case 'Denim': return <DenimIcon {...iconProps} />;
    case 'Faux Fur': return <FauxFurIcon {...iconProps} />;
    default: return <LinenIcon {...iconProps} />;
  }
};
