import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Clear Glass - Crystal clear with sharp reflections
export const ClearGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="clearGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#F8F8FF" stopOpacity="0.9"/>
        <stop offset="50%" stopColor="#FFFFFF" stopOpacity="0.7"/>
        <stop offset="100%" stopColor="#E6F3FF" stopOpacity="0.8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#clearGrad)" rx="2" stroke="#B3D9FF" strokeWidth="0.5"/>
    <polygon points="2,2 12,2 8,12 2,8" fill="#FFFFFF" opacity="0.9"/>
    <polygon points="22,2 22,12 12,8 16,2" fill="#000000" opacity="0.1"/>
    <polygon points="2,22 8,12 12,22" fill="#000000" opacity="0.15"/>
    <polygon points="22,22 16,12 22,12" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="12" cy="12" r="2" fill="#FFFFFF" opacity="0.95"/>
    <ellipse cx="6" cy="6" rx="1.5" ry="1" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="18" cy="18" rx="1" ry="1.5" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Frosted Glass - Etched matte surface
export const FrostedGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <filter id="frostedTexture">
        <feTurbulence baseFrequency="0.9" numOctaves="4" result="noise"/>
        <feDisplacementMap in="SourceGraphic" in2="noise" scale="0.8"/>
      </filter>
    </defs>
    <rect width="24" height="24" fill="#F0F8FF" rx="2" stroke="#D3D3D3" strokeWidth="0.8"/>
    <rect width="24" height="24" fill="#FFFFFF" opacity="0.6" rx="2" filter="url(#frostedTexture)"/>
    <circle cx="6" cy="6" r="2" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="18" cy="8" r="1.5" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="8" cy="16" r="1.8" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="16" cy="18" r="1.2" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="12" cy="4" r="1" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="20" cy="14" r="1.3" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="4" cy="12" r="1.6" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="14" cy="22" r="1.1" fill="#FFFFFF" opacity="0.6"/>
  </svg>
);

// Tempered Glass - Safety glass with stress patterns
export const TemperedGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#E6F3FF" rx="2" stroke="#4169E1" strokeWidth="1"/>
    <circle cx="12" cy="12" r="8" fill="none" stroke="#1E90FF" strokeWidth="0.5" opacity="0.6"/>
    <circle cx="12" cy="12" r="6" fill="none" stroke="#1E90FF" strokeWidth="0.4" opacity="0.5"/>
    <circle cx="12" cy="12" r="4" fill="none" stroke="#1E90FF" strokeWidth="0.3" opacity="0.4"/>
    <circle cx="12" cy="12" r="2" fill="none" stroke="#1E90FF" strokeWidth="0.2" opacity="0.3"/>
    <path d="M12,4 L12,20 M4,12 L20,12" stroke="#1E90FF" strokeWidth="0.3" opacity="0.4"/>
    <path d="M7,7 L17,17 M17,7 L7,17" stroke="#1E90FF" strokeWidth="0.3" opacity="0.4"/>
    <circle cx="6" cy="6" r="0.8" fill="#4169E1" opacity="0.7"/>
    <circle cx="18" cy="6" r="0.6" fill="#4169E1" opacity="0.6"/>
    <circle cx="6" cy="18" r="0.7" fill="#4169E1" opacity="0.8"/>
    <circle cx="18" cy="18" r="0.5" fill="#4169E1" opacity="0.5"/>
  </svg>
);

// Stained Glass - Colorful leaded glass pattern
export const StainedGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#2F2F2F" rx="2"/>
    <path d="M0,0 L12,0 Q16,4 12,8 L8,12 Q4,8 0,12 Z" fill="#FF0000" opacity="0.8" stroke="#000000" strokeWidth="1"/>
    <path d="M12,0 L24,0 L24,12 Q20,8 16,12 L12,8 Q16,4 12,0" fill="#0000FF" opacity="0.8" stroke="#000000" strokeWidth="1"/>
    <path d="M0,12 Q4,8 8,12 L12,16 Q8,20 4,16 L0,24 Z" fill="#00FF00" opacity="0.8" stroke="#000000" strokeWidth="1"/>
    <path d="M8,12 L16,12 Q20,8 24,12 L24,24 L12,24 Q8,20 12,16 L8,12" fill="#FFFF00" opacity="0.8" stroke="#000000" strokeWidth="1"/>
    <path d="M12,16 Q16,20 12,24 L4,16 Q8,20 12,16" fill="#FF00FF" opacity="0.8" stroke="#000000" strokeWidth="1"/>
    <circle cx="6" cy="6" r="1" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="18" cy="6" r="0.8" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="6" cy="18" r="0.9" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="18" cy="18" r="0.7" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Mirrored Glass - Reflective silver backing
export const MirroredGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="mirrorGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E5E5E5"/>
        <stop offset="30%" stopColor="#FFFFFF"/>
        <stop offset="70%" stopColor="#C0C0C0"/>
        <stop offset="100%" stopColor="#A8A8A8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#mirrorGrad)" rx="2" stroke="#808080" strokeWidth="1"/>
    <rect x="2" y="2" width="20" height="20" fill="#FFFFFF" opacity="0.3" rx="1"/>
    <polygon points="4,4 12,4 8,12 4,8" fill="#FFFFFF" opacity="0.9"/>
    <polygon points="20,4 20,12 12,8 16,4" fill="#000000" opacity="0.3"/>
    <polygon points="4,20 8,12 12,20" fill="#000000" opacity="0.4"/>
    <polygon points="20,20 16,12 20,12" fill="#FFFFFF" opacity="0.7"/>
    <ellipse cx="8" cy="8" rx="2" ry="1" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="16" cy="16" rx="1.5" ry="2" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="18" cy="6" r="1" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="6" cy="18" r="0.8" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Textured Glass - Ribbed or patterned surface
export const TexturedGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F0F8FF" rx="2" stroke="#B3D9FF" strokeWidth="0.8"/>
    <path d="M0,2 Q6,0 12,2 Q18,4 24,2 L24,6 Q18,8 12,6 Q6,4 0,6 Z" fill="#E6F3FF" opacity="0.8"/>
    <path d="M0,6 Q6,4 12,6 Q18,8 24,6 L24,10 Q18,12 12,10 Q6,8 0,10 Z" fill="#FFFFFF" opacity="0.6"/>
    <path d="M0,10 Q6,8 12,10 Q18,12 24,10 L24,14 Q18,16 12,14 Q6,12 0,14 Z" fill="#E6F3FF" opacity="0.8"/>
    <path d="M0,14 Q6,12 12,14 Q18,16 24,14 L24,18 Q18,20 12,18 Q6,16 0,18 Z" fill="#FFFFFF" opacity="0.6"/>
    <path d="M0,18 Q6,16 12,18 Q18,20 24,18 L24,22 Q18,24 12,22 Q6,20 0,22 Z" fill="#E6F3FF" opacity="0.8"/>
    <path d="M0,22 Q6,20 12,22 Q18,24 24,22 L24,24 L0,24 Z" fill="#FFFFFF" opacity="0.6"/>
    <ellipse cx="8" cy="4" rx="1.5" ry="0.8" fill="#FFFFFF" opacity="0.9"/>
    <ellipse cx="16" cy="12" rx="1.2" ry="0.6" fill="#FFFFFF" opacity="0.8"/>
    <ellipse cx="20" cy="20" rx="1" ry="0.5" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Colored Glass - Tinted transparent glass
export const ColoredGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="coloredGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF6B6B" stopOpacity="0.7"/>
        <stop offset="25%" stopColor="#4ECDC4" stopOpacity="0.7"/>
        <stop offset="50%" stopColor="#45B7D1" stopOpacity="0.7"/>
        <stop offset="75%" stopColor="#96CEB4" stopOpacity="0.7"/>
        <stop offset="100%" stopColor="#FFEAA7" stopOpacity="0.7"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#coloredGrad)" rx="2" stroke="#DDD" strokeWidth="0.5"/>
    <rect x="0" y="0" width="8" height="8" fill="#FF6B6B" opacity="0.6"/>
    <rect x="8" y="0" width="8" height="8" fill="#4ECDC4" opacity="0.6"/>
    <rect x="16" y="0" width="8" height="8" fill="#45B7D1" opacity="0.6"/>
    <rect x="0" y="8" width="8" height="8" fill="#4ECDC4" opacity="0.6"/>
    <rect x="8" y="8" width="8" height="8" fill="#45B7D1" opacity="0.6"/>
    <rect x="16" y="8" width="8" height="8" fill="#96CEB4" opacity="0.6"/>
    <rect x="0" y="16" width="8" height="8" fill="#45B7D1" opacity="0.6"/>
    <rect x="8" y="16" width="8" height="8" fill="#96CEB4" opacity="0.6"/>
    <rect x="16" y="16" width="8" height="8" fill="#FFEAA7" opacity="0.6"/>
    <circle cx="4" cy="4" r="1" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="12" cy="12" r="1.5" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="20" cy="20" r="1.2" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Laminated Glass - Safety glass with interlayer
export const LaminatedGlassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F0F8FF" rx="2" stroke="#4169E1" strokeWidth="1"/>
    <rect x="2" y="2" width="20" height="8" fill="#E6F3FF" opacity="0.8" stroke="#1E90FF" strokeWidth="0.5"/>
    <rect x="2" y="11" width="20" height="2" fill="#FFE4B5" opacity="0.9" stroke="#DDD26A" strokeWidth="0.3"/>
    <rect x="2" y="14" width="20" height="8" fill="#E6F3FF" opacity="0.8" stroke="#1E90FF" strokeWidth="0.5"/>
    <path d="M2,11 L22,11 M2,13 L22,13" stroke="#F0E68C" strokeWidth="0.5" opacity="0.8"/>
    <circle cx="6" cy="6" r="1" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="18" cy="6" r="0.8" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="6" cy="18" r="0.9" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="18" cy="18" r="0.7" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="12" cy="12" r="0.5" fill="#FFD700" opacity="0.8"/>
    <ellipse cx="10" cy="4" rx="1.5" ry="0.8" fill="#FFFFFF" opacity="0.7"/>
    <ellipse cx="14" cy="20" rx="1.2" ry="0.6" fill="#FFFFFF" opacity="0.6"/>
  </svg>
);

// Glass Icon Mapper
export const getUniqueGlassIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Clear Glass': return <ClearGlassIcon {...iconProps} />;
    case 'Frosted Glass': return <FrostedGlassIcon {...iconProps} />;
    case 'Tempered Glass': return <TemperedGlassIcon {...iconProps} />;
    case 'Stained Glass': return <StainedGlassIcon {...iconProps} />;
    case 'Mirrored Glass': return <MirroredGlassIcon {...iconProps} />;
    case 'Textured Glass': return <TexturedGlassIcon {...iconProps} />;
    case 'Colored Glass': return <ColoredGlassIcon {...iconProps} />;
    case 'Laminated Glass': return <LaminatedGlassIcon {...iconProps} />;
    default: return <ClearGlassIcon {...iconProps} />;
  }
};
