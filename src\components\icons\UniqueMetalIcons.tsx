import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Stainless Steel - Brushed horizontal lines with reflective strips
export const StainlessSteelIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#C0C0C0" rx="2"/>
    <rect x="0" y="0" width="24" height="2" fill="#E5E5E5"/>
    <rect x="0" y="2" width="24" height="2" fill="#B8B8B8"/>
    <rect x="0" y="4" width="24" height="2" fill="#D0D0D0"/>
    <rect x="0" y="6" width="24" height="2" fill="#A8A8A8"/>
    <rect x="0" y="8" width="24" height="2" fill="#E5E5E5"/>
    <rect x="0" y="10" width="24" height="2" fill="#B8B8B8"/>
    <rect x="0" y="12" width="24" height="2" fill="#D0D0D0"/>
    <rect x="0" y="14" width="24" height="2" fill="#A8A8A8"/>
    <rect x="0" y="16" width="24" height="2" fill="#E5E5E5"/>
    <rect x="0" y="18" width="24" height="2" fill="#B8B8B8"/>
    <rect x="0" y="20" width="24" height="2" fill="#D0D0D0"/>
    <rect x="0" y="22" width="24" height="2" fill="#A8A8A8"/>
    <rect x="4" y="0" width="16" height="1" fill="#FFFFFF" opacity="0.6"/>
    <rect x="2" y="12" width="20" height="1" fill="#FFFFFF" opacity="0.4"/>
  </svg>
);

// Chrome - Mirror reflection with geometric shapes
export const ChromeIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="chromeGrad" cx="30%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="50%" stopColor="#E5E5E5"/>
        <stop offset="100%" stopColor="#A8A8A8"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#chromeGrad)" rx="2"/>
    <polygon points="2,2 12,2 8,12 2,8" fill="#FFFFFF" opacity="0.8"/>
    <polygon points="22,2 22,12 12,8 16,2" fill="#000000" opacity="0.2"/>
    <polygon points="2,22 8,12 12,22" fill="#000000" opacity="0.3"/>
    <polygon points="22,22 16,12 22,12" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="12" cy="12" r="3" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="12" cy="12" r="1" fill="#E5E5E5"/>
  </svg>
);

// Brass - Hexagonal bolt head pattern
export const BrassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#B5A642" rx="2"/>
    <polygon points="12,2 18,6 18,12 12,16 6,12 6,6" fill="#FFD700" stroke="#8B7355" strokeWidth="1"/>
    <polygon points="12,4 16,7 16,11 12,14 8,11 8,7" fill="#DAA520" stroke="#6B5B47" strokeWidth="0.8"/>
    <polygon points="12,6 14,8 14,10 12,12 10,10 10,8" fill="#B8860B" stroke="#654321" strokeWidth="0.6"/>
    <circle cx="12" cy="9" r="1" fill="#8B7355"/>
    <circle cx="6" cy="18" r="1.5" fill="#6B5B47" opacity="0.6"/>
    <circle cx="18" cy="20" r="1" fill="#6B5B47" opacity="0.4"/>
    <circle cx="20" cy="6" r="0.8" fill="#6B5B47" opacity="0.3"/>
  </svg>
);

// Copper - Circuit board pattern with oxidation
export const CopperIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#B87333" rx="2"/>
    <path d="M2,6 L8,6 L8,2 L14,2 L14,8 L20,8 L20,14 L14,14 L14,20 L8,20 L8,14 L2,14 Z" 
          fill="none" stroke="#FF7F50" strokeWidth="2"/>
    <path d="M8,6 L14,6 L14,8 L20,8 M8,14 L14,14 L14,8" 
          fill="none" stroke="#FF7F50" strokeWidth="1.5"/>
    <circle cx="5" cy="9" r="1.5" fill="#FF7F50"/>
    <circle cx="19" cy="11" r="1" fill="#FF7F50"/>
    <circle cx="11" cy="17" r="1.2" fill="#FF7F50"/>
    <circle cx="8" cy="8" r="2" fill="#40E0D0" opacity="0.7"/>
    <circle cx="16" cy="16" r="1.5" fill="#40E0D0" opacity="0.6"/>
    <circle cx="20" cy="4" r="1" fill="#40E0D0" opacity="0.5"/>
  </svg>
);

// Bronze - Ancient coin with embossed design
export const BronzeIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="bronzeGrad" cx="40%" cy="40%" r="60%">
        <stop offset="0%" stopColor="#CD7F32"/>
        <stop offset="70%" stopColor="#8B4513"/>
        <stop offset="100%" stopColor="#654321"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#bronzeGrad)" rx="2"/>
    <circle cx="12" cy="12" r="9" fill="#CD7F32" stroke="#654321" strokeWidth="1"/>
    <circle cx="12" cy="12" r="7" fill="none" stroke="#8B4513" strokeWidth="0.8"/>
    <polygon points="12,6 14,10 18,10 15,13 16,17 12,15 8,17 9,13 6,10 10,10" fill="#654321"/>
    <circle cx="12" cy="12" r="2" fill="#8B4513"/>
    <circle cx="6" cy="6" r="1" fill="#2F4F2F" opacity="0.6"/>
    <circle cx="18" cy="18" r="1.2" fill="#2F4F2F" opacity="0.5"/>
  </svg>
);

// Iron - Riveted metal plates
export const IronIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#464451" rx="2"/>
    <rect x="0" y="0" width="12" height="12" fill="#36454F" stroke="#2F2F2F" strokeWidth="1"/>
    <rect x="12" y="0" width="12" height="12" fill="#464451" stroke="#2F2F2F" strokeWidth="1"/>
    <rect x="0" y="12" width="12" height="12" fill="#464451" stroke="#2F2F2F" strokeWidth="1"/>
    <rect x="12" y="12" width="12" height="12" fill="#36454F" stroke="#2F2F2F" strokeWidth="1"/>
    <circle cx="3" cy="3" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="9" cy="3" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="15" cy="3" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="21" cy="3" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="3" cy="9" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="9" cy="9" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="15" cy="9" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="21" cy="9" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="3" cy="15" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="9" cy="15" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="15" cy="15" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="21" cy="15" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="3" cy="21" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="9" cy="21" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="15" cy="21" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="21" cy="21" r="1" fill="#696969" stroke="#2F2F2F" strokeWidth="0.5"/>
    <circle cx="6" cy="6" r="0.8" fill="#8B4513" opacity="0.8"/>
    <circle cx="18" cy="18" r="0.6" fill="#8B4513" opacity="0.6"/>
  </svg>
);

// Aluminum - Diamond plate pattern
export const AluminumIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#A8A8A8" rx="2"/>
    <pattern id="diamond" patternUnits="userSpaceOnUse" width="4" height="4">
      <polygon points="2,0 4,2 2,4 0,2" fill="#C0C0C0"/>
      <polygon points="2,0 4,2 2,4 0,2" fill="none" stroke="#808080" strokeWidth="0.2"/>
    </pattern>
    <rect width="24" height="24" fill="url(#diamond)" rx="2"/>
    <rect x="2" y="2" width="20" height="20" fill="none" stroke="#FFFFFF" strokeWidth="0.5" opacity="0.6"/>
    <polygon points="6,6 8,8 6,10 4,8" fill="#FFFFFF" opacity="0.8"/>
    <polygon points="18,6 20,8 18,10 16,8" fill="#FFFFFF" opacity="0.7"/>
    <polygon points="6,18 8,20 6,22 4,20" fill="#FFFFFF" opacity="0.6"/>
    <polygon points="18,18 20,20 18,22 16,20" fill="#FFFFFF" opacity="0.5"/>
  </svg>
);

// Gold Finish - Ornate baroque pattern
export const GoldFinishIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="goldGrad" cx="50%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFD700"/>
        <stop offset="50%" stopColor="#DAA520"/>
        <stop offset="100%" stopColor="#B8860B"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#goldGrad)" rx="2"/>
    <path d="M12,2 Q8,6 12,10 Q16,6 12,2 M12,14 Q8,18 12,22 Q16,18 12,14" fill="#FFFF00" opacity="0.6"/>
    <path d="M2,12 Q6,8 10,12 Q6,16 2,12 M14,12 Q18,8 22,12 Q18,16 14,12" fill="#FFFF00" opacity="0.6"/>
    <circle cx="12" cy="12" r="3" fill="#FFD700" stroke="#B8860B" strokeWidth="0.8"/>
    <circle cx="12" cy="12" r="1.5" fill="#FFFF00" opacity="0.8"/>
    <circle cx="6" cy="6" r="1" fill="#FFFF00" opacity="0.7"/>
    <circle cx="18" cy="6" r="1" fill="#FFFF00" opacity="0.7"/>
    <circle cx="6" cy="18" r="1" fill="#FFFF00" opacity="0.7"/>
    <circle cx="18" cy="18" r="1" fill="#FFFF00" opacity="0.7"/>
  </svg>
);

// Silver Finish - Art deco sunburst pattern
export const SilverFinishIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#C0C0C0" rx="2"/>
    <path d="M12,2 L12,12 L2,12 Z" fill="#E5E5E5"/>
    <path d="M12,2 L12,12 L22,12 Z" fill="#A8A8A8"/>
    <path d="M12,12 L2,12 L12,22 Z" fill="#A8A8A8"/>
    <path d="M12,12 L22,12 L12,22 Z" fill="#E5E5E5"/>
    <path d="M12,2 L12,12 L2,2 Z" fill="#FFFFFF" opacity="0.6"/>
    <path d="M12,2 L12,12 L22,2 Z" fill="#808080" opacity="0.4"/>
    <path d="M12,12 L2,22 L12,22 Z" fill="#808080" opacity="0.4"/>
    <path d="M12,12 L22,22 L12,22 Z" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="12" cy="12" r="2" fill="#FFFFFF"/>
    <circle cx="12" cy="12" r="1" fill="#C0C0C0"/>
  </svg>
);

// Pewter - Hammered texture with dents
export const PewterIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#96A8A1" rx="2"/>
    <circle cx="6" cy="4" r="2" fill="#A8B8B1" opacity="0.8"/>
    <circle cx="18" cy="6" r="1.5" fill="#84948D" opacity="0.7"/>
    <circle cx="4" cy="12" r="2.5" fill="#A8B8B1" opacity="0.6"/>
    <circle cx="16" cy="14" r="2" fill="#84948D" opacity="0.8"/>
    <circle cx="8" cy="20" r="1.8" fill="#A8B8B1" opacity="0.7"/>
    <circle cx="20" cy="18" r="1.2" fill="#84948D" opacity="0.6"/>
    <circle cx="12" cy="8" r="1.5" fill="#A8B8B1" opacity="0.5"/>
    <circle cx="14" cy="22" r="1" fill="#84948D" opacity="0.4"/>
    <ellipse cx="10" cy="16" rx="1.5" ry="1" fill="#A8B8B1" opacity="0.6"/>
    <ellipse cx="22" cy="10" rx="1" ry="1.5" fill="#84948D" opacity="0.5"/>
  </svg>
);

// Titanium - Molecular structure pattern
export const TitaniumIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#878681" rx="2"/>
    <circle cx="6" cy="6" r="1.5" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <circle cx="18" cy="6" r="1.5" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <circle cx="6" cy="18" r="1.5" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <circle cx="18" cy="18" r="1.5" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <circle cx="12" cy="12" r="2" fill="#C0C0C0" stroke="#696969" strokeWidth="0.8"/>
    <circle cx="12" cy="4" r="1" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <circle cx="12" cy="20" r="1" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <circle cx="4" cy="12" r="1" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <circle cx="20" cy="12" r="1" fill="#A8A8A8" stroke="#696969" strokeWidth="0.5"/>
    <line x1="6" y1="6" x2="12" y2="12" stroke="#696969" strokeWidth="0.8"/>
    <line x1="18" y1="6" x2="12" y2="12" stroke="#696969" strokeWidth="0.8"/>
    <line x1="6" y1="18" x2="12" y2="12" stroke="#696969" strokeWidth="0.8"/>
    <line x1="18" y1="18" x2="12" y2="12" stroke="#696969" strokeWidth="0.8"/>
    <line x1="12" y1="4" x2="12" y2="12" stroke="#696969" strokeWidth="0.6"/>
    <line x1="12" y1="20" x2="12" y2="12" stroke="#696969" strokeWidth="0.6"/>
    <line x1="4" y1="12" x2="12" y2="12" stroke="#696969" strokeWidth="0.6"/>
    <line x1="20" y1="12" x2="12" y2="12" stroke="#696969" strokeWidth="0.6"/>
  </svg>
);

// Wrought Iron - Decorative scrollwork pattern
export const WroughtIronIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#2F2F2F" rx="2"/>
    <path d="M4,4 Q8,2 12,4 Q16,6 20,4 Q22,8 20,12 Q18,16 20,20 Q16,22 12,20 Q8,18 4,20 Q2,16 4,12 Q6,8 4,4" 
          fill="none" stroke="#464451" strokeWidth="2"/>
    <path d="M8,8 Q12,6 16,8 Q18,12 16,16 Q12,18 8,16 Q6,12 8,8" 
          fill="none" stroke="#464451" strokeWidth="1.5"/>
    <circle cx="4" cy="4" r="1" fill="#464451"/>
    <circle cx="20" cy="4" r="1" fill="#464451"/>
    <circle cx="4" cy="20" r="1" fill="#464451"/>
    <circle cx="20" cy="20" r="1" fill="#464451"/>
    <circle cx="12" cy="12" r="1.5" fill="#464451"/>
    <path d="M12,2 Q14,4 12,6 Q10,4 12,2" fill="#464451"/>
    <path d="M12,18 Q14,20 12,22 Q10,20 12,18" fill="#464451"/>
    <path d="M2,12 Q4,10 6,12 Q4,14 2,12" fill="#464451"/>
    <path d="M18,12 Q20,10 22,12 Q20,14 18,12" fill="#464451"/>
  </svg>
);

// Metal Icon Mapper
export const getUniqueMetalIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Stainless Steel': return <StainlessSteelIcon {...iconProps} />;
    case 'Chrome': return <ChromeIcon {...iconProps} />;
    case 'Brass': return <BrassIcon {...iconProps} />;
    case 'Copper': return <CopperIcon {...iconProps} />;
    case 'Bronze': return <BronzeIcon {...iconProps} />;
    case 'Iron': return <IronIcon {...iconProps} />;
    case 'Aluminum': return <AluminumIcon {...iconProps} />;
    case 'Gold Finish': return <GoldFinishIcon {...iconProps} />;
    case 'Silver Finish': return <SilverFinishIcon {...iconProps} />;
    case 'Pewter': return <PewterIcon {...iconProps} />;
    case 'Titanium': return <TitaniumIcon {...iconProps} />;
    case 'Wrought Iron': return <WroughtIronIcon {...iconProps} />;
    default: return <StainlessSteelIcon {...iconProps} />;
  }
};
