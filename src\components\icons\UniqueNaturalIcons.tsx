import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Rattan - Curved woven cane strips
export const RattanIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DEB887" rx="2"/>
    <path d="M0,4 Q6,2 12,4 Q18,6 24,4 Q20,8 12,6 Q4,4 0,8" fill="#D2B48C" opacity="0.8"/>
    <path d="M0,8 Q6,6 12,8 Q18,10 24,8 Q20,12 12,10 Q4,8 0,12" fill="#F4A460" opacity="0.7"/>
    <path d="M0,12 Q6,10 12,12 Q18,14 24,12 Q20,16 12,14 Q4,12 0,16" fill="#D2B48C" opacity="0.8"/>
    <path d="M0,16 Q6,14 12,16 Q18,18 24,16 Q20,20 12,18 Q4,16 0,20" fill="#F4A460" opacity="0.7"/>
    <path d="M0,20 Q6,18 12,20 Q18,22 24,20 Q20,24 12,22 Q4,20 0,24" fill="#D2B48C" opacity="0.8"/>
    <path d="M4,0 Q6,6 4,12 Q6,18 4,24" stroke="#CD853F" strokeWidth="1" fill="none"/>
    <path d="M8,0 Q10,6 8,12 Q10,18 8,24" stroke="#CD853F" strokeWidth="1" fill="none"/>
    <path d="M12,0 Q14,6 12,12 Q14,18 12,24" stroke="#CD853F" strokeWidth="1" fill="none"/>
    <path d="M16,0 Q18,6 16,12 Q18,18 16,24" stroke="#CD853F" strokeWidth="1" fill="none"/>
    <path d="M20,0 Q22,6 20,12 Q22,18 20,24" stroke="#CD853F" strokeWidth="1" fill="none"/>
  </svg>
);

// Wicker - Basket weave pattern
export const WickerIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F4A460" rx="2"/>
    <path d="M0,0 L4,4 L0,8 L4,12 L0,16 L4,20 L0,24" stroke="#D2B48C" strokeWidth="2" fill="none"/>
    <path d="M4,0 L8,4 L4,8 L8,12 L4,16 L8,20 L4,24" stroke="#DEB887" strokeWidth="2" fill="none"/>
    <path d="M8,0 L12,4 L8,8 L12,12 L8,16 L12,20 L8,24" stroke="#D2B48C" strokeWidth="2" fill="none"/>
    <path d="M12,0 L16,4 L12,8 L16,12 L12,16 L16,20 L12,24" stroke="#DEB887" strokeWidth="2" fill="none"/>
    <path d="M16,0 L20,4 L16,8 L20,12 L16,16 L20,20 L16,24" stroke="#D2B48C" strokeWidth="2" fill="none"/>
    <path d="M20,0 L24,4 L20,8 L24,12 L20,16 L24,20 L20,24" stroke="#DEB887" strokeWidth="2" fill="none"/>
    <path d="M0,0 L4,4 L8,0 L12,4 L16,0 L20,4 L24,0" stroke="#CD853F" strokeWidth="2" fill="none"/>
    <path d="M0,8 L4,12 L8,8 L12,12 L16,8 L20,12 L24,8" stroke="#CD853F" strokeWidth="2" fill="none"/>
    <path d="M0,16 L4,20 L8,16 L12,20 L16,16 L20,20 L24,16" stroke="#CD853F" strokeWidth="2" fill="none"/>
    <path d="M0,24 L4,20 L8,24 L12,20 L16,24 L20,20 L24,24" stroke="#CD853F" strokeWidth="2" fill="none"/>
  </svg>
);

// Jute - Coarse fiber rope texture
export const JuteIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DDD26A" rx="2"/>
    <path d="M2,2 Q4,6 2,10 Q4,14 2,18 Q4,22 2,24" stroke="#B8860B" strokeWidth="1.5" fill="none"/>
    <path d="M6,0 Q8,4 6,8 Q8,12 6,16 Q8,20 6,24" stroke="#DAA520" strokeWidth="1.5" fill="none"/>
    <path d="M10,2 Q12,6 10,10 Q12,14 10,18 Q12,22 10,24" stroke="#B8860B" strokeWidth="1.5" fill="none"/>
    <path d="M14,0 Q16,4 14,8 Q16,12 14,16 Q16,20 14,24" stroke="#DAA520" strokeWidth="1.5" fill="none"/>
    <path d="M18,2 Q20,6 18,10 Q20,14 18,18 Q20,22 18,24" stroke="#B8860B" strokeWidth="1.5" fill="none"/>
    <path d="M22,0 Q24,4 22,8 Q24,12 22,16 Q24,20 22,24" stroke="#DAA520" strokeWidth="1.5" fill="none"/>
    <circle cx="3" cy="4" r="0.5" fill="#8B7355" opacity="0.8"/>
    <circle cx="7" cy="8" r="0.4" fill="#8B7355" opacity="0.7"/>
    <circle cx="11" cy="12" r="0.6" fill="#8B7355" opacity="0.9"/>
    <circle cx="15" cy="16" r="0.3" fill="#8B7355" opacity="0.6"/>
    <circle cx="19" cy="20" r="0.5" fill="#8B7355" opacity="0.8"/>
    <circle cx="5" cy="22" r="0.4" fill="#8B7355" opacity="0.7"/>
    <circle cx="9" cy="6" r="0.3" fill="#8B7355" opacity="0.5"/>
    <circle cx="13" cy="10" r="0.5" fill="#8B7355" opacity="0.8"/>
    <circle cx="17" cy="14" r="0.4" fill="#8B7355" opacity="0.6"/>
    <circle cx="21" cy="18" r="0.6" fill="#8B7355" opacity="0.9"/>
  </svg>
);

// Sisal - Agave fiber with spiky texture
export const SisalIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F0E68C" rx="2"/>
    <path d="M2,12 L6,8 L10,12 L14,8 L18,12 L22,8" stroke="#DDD26A" strokeWidth="1.5" fill="none"/>
    <path d="M2,12 L6,16 L10,12 L14,16 L18,12 L22,16" stroke="#DDD26A" strokeWidth="1.5" fill="none"/>
    <path d="M0,6 L4,2 L8,6 L12,2 L16,6 L20,2 L24,6" stroke="#B8860B" strokeWidth="1" fill="none"/>
    <path d="M0,18 L4,22 L8,18 L12,22 L16,18 L20,22 L24,18" stroke="#B8860B" strokeWidth="1" fill="none"/>
    <polygon points="4,2 6,6 4,10 2,6" fill="#DAA520" opacity="0.7"/>
    <polygon points="8,6 10,10 8,14 6,10" fill="#F4A460" opacity="0.6"/>
    <polygon points="12,2 14,6 12,10 10,6" fill="#DAA520" opacity="0.7"/>
    <polygon points="16,6 18,10 16,14 14,10" fill="#F4A460" opacity="0.6"/>
    <polygon points="20,2 22,6 20,10 18,6" fill="#DAA520" opacity="0.7"/>
    <polygon points="4,14 6,18 4,22 2,18" fill="#F4A460" opacity="0.6"/>
    <polygon points="8,18 10,22 8,24 6,22" fill="#DAA520" opacity="0.7"/>
    <polygon points="12,14 14,18 12,22 10,18" fill="#F4A460" opacity="0.6"/>
    <polygon points="16,18 18,22 16,24 14,22" fill="#DAA520" opacity="0.7"/>
    <polygon points="20,14 22,18 20,22 18,18" fill="#F4A460" opacity="0.6"/>
  </svg>
);

// Cork - Cellular bark texture with pores
export const CorkIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DEB887" rx="2"/>
    <ellipse cx="4" cy="3" rx="1.5" ry="1" fill="#D2B48C" opacity="0.8"/>
    <ellipse cx="8" cy="2" rx="1" ry="1.5" fill="#F4A460" opacity="0.7"/>
    <ellipse cx="12" cy="4" rx="1.8" ry="1.2" fill="#D2B48C" opacity="0.9"/>
    <ellipse cx="16" cy="3" rx="1.2" ry="1.6" fill="#F4A460" opacity="0.6"/>
    <ellipse cx="20" cy="5" rx="1.6" ry="1" fill="#D2B48C" opacity="0.8"/>
    <ellipse cx="2" cy="8" rx="1.3" ry="1.4" fill="#F4A460" opacity="0.7"/>
    <ellipse cx="6" cy="7" rx="1.7" ry="1.1" fill="#D2B48C" opacity="0.8"/>
    <ellipse cx="10" cy="9" rx="1.1" ry="1.7" fill="#F4A460" opacity="0.6"/>
    <ellipse cx="14" cy="8" rx="1.5" ry="1.3" fill="#D2B48C" opacity="0.9"/>
    <ellipse cx="18" cy="10" rx="1.4" ry="1" fill="#F4A460" opacity="0.7"/>
    <ellipse cx="22" cy="9" rx="1" ry="1.5" fill="#D2B48C" opacity="0.8"/>
    <ellipse cx="3" cy="13" rx="1.6" ry="1.2" fill="#D2B48C" opacity="0.7"/>
    <ellipse cx="7" cy="12" rx="1.2" ry="1.6" fill="#F4A460" opacity="0.8"/>
    <ellipse cx="11" cy="14" rx="1.8" ry="1.1" fill="#D2B48C" opacity="0.6"/>
    <ellipse cx="15" cy="13" rx="1.1" ry="1.4" fill="#F4A460" opacity="0.9"/>
    <ellipse cx="19" cy="15" rx="1.5" ry="1.2" fill="#D2B48C" opacity="0.7"/>
    <ellipse cx="1" cy="18" rx="1.4" ry="1.3" fill="#F4A460" opacity="0.8"/>
    <ellipse cx="5" cy="17" rx="1.7" ry="1" fill="#D2B48C" opacity="0.6"/>
    <ellipse cx="9" cy="19" rx="1" ry="1.6" fill="#F4A460" opacity="0.9"/>
    <ellipse cx="13" cy="18" rx="1.6" ry="1.4" fill="#D2B48C" opacity="0.7"/>
    <ellipse cx="17" cy="20" rx="1.3" ry="1.1" fill="#F4A460" opacity="0.8"/>
    <ellipse cx="21" cy="19" rx="1.1" ry="1.5" fill="#D2B48C" opacity="0.6"/>
    <ellipse cx="4" cy="22" rx="1.5" ry="1.2" fill="#D2B48C" opacity="0.9"/>
    <ellipse cx="8" cy="21" rx="1.2" ry="1.4" fill="#F4A460" opacity="0.7"/>
    <ellipse cx="12" cy="23" rx="1.8" ry="1" fill="#D2B48C" opacity="0.8"/>
    <ellipse cx="16" cy="22" rx="1.1" ry="1.3" fill="#F4A460" opacity="0.6"/>
    <ellipse cx="20" cy="24" rx="1.4" ry="1.1" fill="#D2B48C" opacity="0.9"/>
  </svg>
);

// Seagrass - Marine grass with flowing blades
export const SeagrassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#9ACD32" rx="2"/>
    <path d="M2,24 Q4,20 2,16 Q4,12 2,8 Q4,4 2,0" stroke="#228B22" strokeWidth="1.5" fill="none"/>
    <path d="M6,24 Q8,18 6,12 Q8,6 6,0" stroke="#32CD32" strokeWidth="1.5" fill="none"/>
    <path d="M10,24 Q12,20 10,16 Q12,12 10,8 Q12,4 10,0" stroke="#228B22" strokeWidth="1.5" fill="none"/>
    <path d="M14,24 Q16,18 14,12 Q16,6 14,0" stroke="#32CD32" strokeWidth="1.5" fill="none"/>
    <path d="M18,24 Q20,20 18,16 Q20,12 18,8 Q20,4 18,0" stroke="#228B22" strokeWidth="1.5" fill="none"/>
    <path d="M22,24 Q24,18 22,12 Q24,6 22,0" stroke="#32CD32" strokeWidth="1.5" fill="none"/>
    <ellipse cx="3" cy="20" rx="0.8" ry="2" fill="#ADFF2F" opacity="0.7"/>
    <ellipse cx="7" cy="16" rx="0.6" ry="1.5" fill="#7CFC00" opacity="0.8"/>
    <ellipse cx="11" cy="18" rx="0.9" ry="2.2" fill="#ADFF2F" opacity="0.6"/>
    <ellipse cx="15" cy="14" rx="0.7" ry="1.8" fill="#7CFC00" opacity="0.9"/>
    <ellipse cx="19" cy="20" rx="0.8" ry="2" fill="#ADFF2F" opacity="0.7"/>
    <ellipse cx="23" cy="16" rx="0.5" ry="1.5" fill="#7CFC00" opacity="0.8"/>
    <ellipse cx="5" cy="8" rx="0.6" ry="1.8" fill="#ADFF2F" opacity="0.8"/>
    <ellipse cx="9" cy="4" rx="0.8" ry="2" fill="#7CFC00" opacity="0.7"/>
    <ellipse cx="13" cy="6" rx="0.7" ry="1.6" fill="#ADFF2F" opacity="0.9"/>
    <ellipse cx="17" cy="2" rx="0.5" ry="1.4" fill="#7CFC00" opacity="0.6"/>
    <ellipse cx="21" cy="8" rx="0.9" ry="2.2" fill="#ADFF2F" opacity="0.8"/>
  </svg>
);

// Hemp - Strong fiber with linear structure
export const HempIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#8FBC8F" rx="2"/>
    <path d="M2,0 L2,24 M6,0 L6,24 M10,0 L10,24 M14,0 L14,24 M18,0 L18,24 M22,0 L22,24" 
          stroke="#556B2F" strokeWidth="1.5" opacity="0.8"/>
    <path d="M0,4 L24,4 M0,8 L24,8 M0,12 L24,12 M0,16 L24,16 M0,20 L24,20" 
          stroke="#6B8E23" strokeWidth="1" opacity="0.6"/>
    <rect x="0" y="0" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="2" y="0" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="4" y="0" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="6" y="0" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="8" y="0" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="10" y="0" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="12" y="0" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="14" y="0" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="16" y="0" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="18" y="0" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="20" y="0" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="22" y="0" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="0" y="4" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="2" y="4" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="4" y="4" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="6" y="4" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="8" y="4" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="10" y="4" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="12" y="4" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="14" y="4" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="16" y="4" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="18" y="4" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="20" y="4" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="22" y="4" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="0" y="8" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="2" y="8" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="4" y="8" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="6" y="8" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="8" y="8" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="10" y="8" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="12" y="8" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="14" y="8" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="16" y="8" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="18" y="8" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="20" y="8" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="22" y="8" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="0" y="12" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="2" y="12" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="4" y="12" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="6" y="12" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="8" y="12" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="10" y="12" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="12" y="12" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="14" y="12" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="16" y="12" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="18" y="12" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="20" y="12" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="22" y="12" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="0" y="16" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="2" y="16" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="4" y="16" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="6" y="16" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="8" y="16" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="10" y="16" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="12" y="16" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="14" y="16" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="16" y="16" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="18" y="16" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="20" y="16" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="22" y="16" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="0" y="20" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="2" y="20" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="4" y="20" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="6" y="20" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="8" y="20" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="10" y="20" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="12" y="20" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="14" y="20" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="16" y="20" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="18" y="20" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
    <rect x="20" y="20" width="2" height="4" fill="#8FBC8F" opacity="0.8"/>
    <rect x="22" y="20" width="2" height="4" fill="#9ACD32" opacity="0.7"/>
  </svg>
);

// Cane - Bamboo-like segments
export const CaneIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F4A460" rx="2"/>
    <circle cx="12" cy="4" r="3" fill="#DEB887" stroke="#CD853F" strokeWidth="1"/>
    <circle cx="12" cy="12" r="3" fill="#D2B48C" stroke="#CD853F" strokeWidth="1"/>
    <circle cx="12" cy="20" r="3" fill="#DEB887" stroke="#CD853F" strokeWidth="1"/>
    <rect x="10" y="7" width="4" height="2" fill="#CD853F"/>
    <rect x="10" y="15" width="4" height="2" fill="#CD853F"/>
    <path d="M9,4 L9,12 M15,4 L15,12" stroke="#8B7355" strokeWidth="0.8"/>
    <path d="M9,12 L9,20 M15,12 L15,20" stroke="#8B7355" strokeWidth="0.8"/>
    <circle cx="12" cy="4" r="1.5" fill="#F5DEB3" opacity="0.8"/>
    <circle cx="12" cy="12" r="1.5" fill="#F5DEB3" opacity="0.8"/>
    <circle cx="12" cy="20" r="1.5" fill="#F5DEB3" opacity="0.8"/>
    <ellipse cx="6" cy="6" rx="1" ry="2" fill="#DEB887" opacity="0.6"/>
    <ellipse cx="18" cy="6" rx="1" ry="2" fill="#DEB887" opacity="0.6"/>
    <ellipse cx="6" cy="18" rx="1" ry="2" fill="#DEB887" opacity="0.6"/>
    <ellipse cx="18" cy="18" rx="1" ry="2" fill="#DEB887" opacity="0.6"/>
  </svg>
);

// Rush - Thin marsh grass bundles
export const RushIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#228B22" rx="2"/>
    <path d="M1,24 L1,0 M3,24 L3,0 M5,24 L5,0 M7,24 L7,0 M9,24 L9,0 M11,24 L11,0 M13,24 L13,0 M15,24 L15,0 M17,24 L17,0 M19,24 L19,0 M21,24 L21,0 M23,24 L23,0" 
          stroke="#32CD32" strokeWidth="0.8" opacity="0.8"/>
    <path d="M2,24 Q2,18 2,12 Q2,6 2,0" stroke="#9ACD32" strokeWidth="0.6" fill="none"/>
    <path d="M4,24 Q4,20 4,16 Q4,12 4,8 Q4,4 4,0" stroke="#ADFF2F" strokeWidth="0.6" fill="none"/>
    <path d="M6,24 Q6,18 6,12 Q6,6 6,0" stroke="#9ACD32" strokeWidth="0.6" fill="none"/>
    <path d="M8,24 Q8,20 8,16 Q8,12 8,8 Q8,4 8,0" stroke="#ADFF2F" strokeWidth="0.6" fill="none"/>
    <path d="M10,24 Q10,18 10,12 Q10,6 10,0" stroke="#9ACD32" strokeWidth="0.6" fill="none"/>
    <path d="M12,24 Q12,20 12,16 Q12,12 12,8 Q12,4 12,0" stroke="#ADFF2F" strokeWidth="0.6" fill="none"/>
    <path d="M14,24 Q14,18 14,12 Q14,6 14,0" stroke="#9ACD32" strokeWidth="0.6" fill="none"/>
    <path d="M16,24 Q16,20 16,16 Q16,12 16,8 Q16,4 16,0" stroke="#ADFF2F" strokeWidth="0.6" fill="none"/>
    <path d="M18,24 Q18,18 18,12 Q18,6 18,0" stroke="#9ACD32" strokeWidth="0.6" fill="none"/>
    <path d="M20,24 Q20,20 20,16 Q20,12 20,8 Q20,4 20,0" stroke="#ADFF2F" strokeWidth="0.6" fill="none"/>
    <path d="M22,24 Q22,18 22,12 Q22,6 22,0" stroke="#9ACD32" strokeWidth="0.6" fill="none"/>
    <ellipse cx="2" cy="20" rx="0.5" ry="1.5" fill="#7CFC00" opacity="0.7"/>
    <ellipse cx="6" cy="16" rx="0.4" ry="1.2" fill="#7CFC00" opacity="0.8"/>
    <ellipse cx="10" cy="18" rx="0.6" ry="1.8" fill="#7CFC00" opacity="0.6"/>
    <ellipse cx="14" cy="14" rx="0.3" ry="1" fill="#7CFC00" opacity="0.9"/>
    <ellipse cx="18" cy="20" rx="0.5" ry="1.5" fill="#7CFC00" opacity="0.7"/>
    <ellipse cx="22" cy="16" rx="0.4" ry="1.2" fill="#7CFC00" opacity="0.8"/>
  </svg>
);

// Natural Icon Mapper
export const getUniqueNaturalIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Rattan': return <RattanIcon {...iconProps} />;
    case 'Wicker': return <WickerIcon {...iconProps} />;
    case 'Jute': return <JuteIcon {...iconProps} />;
    case 'Sisal': return <SisalIcon {...iconProps} />;
    case 'Cork': return <CorkIcon {...iconProps} />;
    case 'Seagrass': return <SeagrassIcon {...iconProps} />;
    case 'Hemp': return <HempIcon {...iconProps} />;
    case 'Cane': return <CaneIcon {...iconProps} />;
    case 'Rush': return <RushIcon {...iconProps} />;
    default: return <RattanIcon {...iconProps} />;
  }
};
