import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// SCULPTURE MATERIALS - Each with unique artistic characteristics

// Bronze Sculpture - Classic metallic sculpture with patina
export const BronzeSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="bronzeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#CD7F32" />
        <stop offset="50%" stopColor="#B8860B" />
        <stop offset="100%" stopColor="#8B4513" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#bronzeGrad)" rx="8" />
    <path d="M24 8 L32 16 L28 32 L20 32 L16 16 Z" fill="#A0522D" opacity="0.8" />
    <circle cx="24" cy="20" r="6" fill="#CD853F" opacity="0.9" />
    <path d="M18 32 L30 32 L28 40 L20 40 Z" fill="#8B4513" />
    <circle cx="20" cy="12" r="2" fill="#FFD700" opacity="0.6" />
    <circle cx="28" cy="24" r="1.5" fill="#DAA520" opacity="0.7" />
  </svg>
);

// Marble Sculpture - Classical white marble with veining
export const MarbleSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="marbleGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#F8F8FF" />
        <stop offset="50%" stopColor="#E6E6FA" />
        <stop offset="100%" stopColor="#DCDCDC" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#marbleGrad)" rx="8" />
    <path d="M24 6 L34 14 L30 36 L18 36 L14 14 Z" fill="#F5F5F5" opacity="0.9" />
    <ellipse cx="24" cy="18" rx="8" ry="6" fill="#FFFFFF" />
    <path d="M16 36 L32 36 L30 42 L18 42 Z" fill="#E0E0E0" />
    <path d="M8 12 Q24 8 40 16" stroke="#C0C0C0" strokeWidth="1" fill="none" opacity="0.6" />
    <path d="M6 24 Q24 20 42 28" stroke="#B0B0B0" strokeWidth="1" fill="none" opacity="0.5" />
    <path d="M10 36 Q24 32 38 40" stroke="#A0A0A0" strokeWidth="1" fill="none" opacity="0.4" />
  </svg>
);

// Clay Sculpture - Earthy terracotta clay
export const ClaySculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="clayGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#D2691E" />
        <stop offset="50%" stopColor="#CD853F" />
        <stop offset="100%" stopColor="#A0522D" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#clayGrad)" rx="8" />
    <path d="M24 10 L30 18 L26 38 L22 38 L18 18 Z" fill="#DEB887" opacity="0.8" />
    <ellipse cx="24" cy="22" rx="6" ry="4" fill="#F4A460" />
    <rect x="20" y="38" width="8" height="6" fill="#8B4513" rx="2" />
    <circle cx="22" cy="16" r="1" fill="#CD853F" opacity="0.7" />
    <circle cx="26" cy="28" r="1" fill="#D2691E" opacity="0.6" />
    <path d="M18 20 Q24 18 30 22" stroke="#A0522D" strokeWidth="1" fill="none" opacity="0.5" />
  </svg>
);

// Ceramic Sculpture - Glazed ceramic with artistic patterns
export const CeramicSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="ceramicGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#4682B4" />
        <stop offset="50%" stopColor="#5F9EA0" />
        <stop offset="100%" stopColor="#2F4F4F" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#ceramicGrad)" rx="8" />
    <path d="M24 8 L32 16 L28 36 L20 36 L16 16 Z" fill="#87CEEB" opacity="0.8" />
    <ellipse cx="24" cy="20" rx="7" ry="5" fill="#B0E0E6" />
    <rect x="18" y="36" width="12" height="6" fill="#2F4F4F" rx="3" />
    <circle cx="20" cy="14" r="2" fill="#00CED1" opacity="0.7" />
    <circle cx="28" cy="26" r="1.5" fill="#20B2AA" opacity="0.8" />
    <path d="M16 18 L32 18" stroke="#FFFFFF" strokeWidth="2" opacity="0.6" />
    <path d="M18 24 L30 24" stroke="#FFFFFF" strokeWidth="1.5" opacity="0.5" />
  </svg>
);

// Wood Carving - Natural wood with carved details
export const WoodCarvingIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="woodCarvingGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#DEB887" />
        <stop offset="50%" stopColor="#D2691E" />
        <stop offset="100%" stopColor="#8B4513" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#woodCarvingGrad)" rx="8" />
    <path d="M24 6 L34 12 L32 38 L16 38 L14 12 Z" fill="#F4A460" opacity="0.9" />
    <ellipse cx="24" cy="18" rx="8" ry="6" fill="#DEB887" />
    <rect x="16" y="38" width="16" height="6" fill="#8B4513" rx="2" />
    <path d="M16 12 Q24 8 32 12" stroke="#A0522D" strokeWidth="2" fill="none" />
    <path d="M18 20 Q24 16 30 20" stroke="#CD853F" strokeWidth="1.5" fill="none" />
    <path d="M20 28 Q24 24 28 28" stroke="#D2691E" strokeWidth="1" fill="none" />
    <circle cx="22" cy="14" r="1" fill="#8B4513" />
    <circle cx="26" cy="22" r="1" fill="#A0522D" />
  </svg>
);

// Stone Carving - Rough hewn stone sculpture
export const StoneCarvingIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="stoneCarvingGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#708090" />
        <stop offset="50%" stopColor="#696969" />
        <stop offset="100%" stopColor="#2F4F4F" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#stoneCarvingGrad)" rx="8" />
    <path d="M24 8 L36 14 L34 36 L14 36 L12 14 Z" fill="#A9A9A9" opacity="0.8" />
    <ellipse cx="24" cy="20" rx="9" ry="7" fill="#C0C0C0" />
    <rect x="14" y="36" width="20" height="8" fill="#2F4F4F" rx="2" />
    <polygon points="20,12 24,8 28,12 26,16 22,16" fill="#DCDCDC" opacity="0.7" />
    <circle cx="18" cy="18" r="1.5" fill="#696969" />
    <circle cx="30" cy="24" r="1" fill="#778899" />
    <path d="M14 22 L34 22" stroke="#2F4F4F" strokeWidth="1" opacity="0.6" />
  </svg>
);

// Metal Sculpture - Modern abstract metal art
export const MetalSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="metalSculptureGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#C0C0C0" />
        <stop offset="50%" stopColor="#A8A8A8" />
        <stop offset="100%" stopColor="#696969" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#metalSculptureGrad)" rx="8" />
    <path d="M12 40 L24 8 L36 40 Z" fill="#E5E5E5" opacity="0.8" />
    <path d="M18 32 L24 16 L30 32 Z" fill="#DCDCDC" />
    <circle cx="24" cy="24" r="6" fill="#F5F5F5" opacity="0.9" />
    <rect x="20" y="40" width="8" height="4" fill="#2F4F4F" />
    <path d="M16 20 L32 28" stroke="#FFFFFF" strokeWidth="2" opacity="0.7" />
    <path d="M32 20 L16 28" stroke="#FFFFFF" strokeWidth="2" opacity="0.7" />
    <circle cx="20" cy="18" r="1" fill="#FFD700" />
    <circle cx="28" cy="30" r="1" fill="#FFD700" />
  </svg>
);

// Glass Sculpture - Transparent crystal-like glass art
export const GlassSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="glassSculptureGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E0F6FF" />
        <stop offset="50%" stopColor="#B0E0E6" />
        <stop offset="100%" stopColor="#87CEEB" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#glassSculptureGrad)" rx="8" />
    <path d="M24 6 L36 18 L30 40 L18 40 L12 18 Z" fill="#F0F8FF" opacity="0.9" />
    <ellipse cx="24" cy="22" rx="8" ry="6" fill="#FFFFFF" opacity="0.8" />
    <rect x="18" y="40" width="12" height="4" fill="#4682B4" opacity="0.6" />
    <path d="M16 14 L32 26" stroke="#00BFFF" strokeWidth="1" opacity="0.7" />
    <path d="M32 14 L16 26" stroke="#00BFFF" strokeWidth="1" opacity="0.7" />
    <circle cx="20" cy="16" r="2" fill="#FFFFFF" opacity="0.9" />
    <circle cx="28" cy="28" r="1.5" fill="#87CEFA" opacity="0.8" />
    <path d="M24 10 L24 34" stroke="#FFFFFF" strokeWidth="2" opacity="0.6" />
  </svg>
);

// Resin Sculpture - Modern synthetic resin art
export const ResinSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="resinGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFE4B5" />
        <stop offset="50%" stopColor="#DDA0DD" />
        <stop offset="100%" stopColor="#9370DB" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#resinGrad)" rx="8" />
    <path d="M24 8 L34 16 L30 38 L18 38 L14 16 Z" fill="#F0E68C" opacity="0.8" />
    <ellipse cx="24" cy="22" rx="7" ry="5" fill="#FFFFE0" opacity="0.9" />
    <rect x="18" y="38" width="12" height="6" fill="#8A2BE2" rx="2" />
    <circle cx="22" cy="16" r="2" fill="#FF69B4" opacity="0.7" />
    <circle cx="26" cy="28" r="1.5" fill="#DA70D6" opacity="0.8" />
    <path d="M16 20 Q24 16 32 20" stroke="#FFFFFF" strokeWidth="1.5" opacity="0.6" />
  </svg>
);

// Plaster Sculpture - White plaster cast
export const PlasterSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="plasterGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFAF0" />
        <stop offset="50%" stopColor="#F5F5DC" />
        <stop offset="100%" stopColor="#E6E6FA" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#plasterGrad)" rx="8" />
    <path d="M24 10 L32 18 L28 36 L20 36 L16 18 Z" fill="#FFFFFF" opacity="0.9" />
    <ellipse cx="24" cy="22" rx="6" ry="4" fill="#FFFAFA" />
    <rect x="20" y="36" width="8" height="6" fill="#D3D3D3" rx="2" />
    <path d="M18 20 Q24 18 30 20" stroke="#C0C0C0" strokeWidth="1" opacity="0.5" />
    <circle cx="22" cy="16" r="1" fill="#DCDCDC" />
    <circle cx="26" cy="26" r="1" fill="#DCDCDC" />
  </svg>
);

// Wire Sculpture - Abstract wire art
export const WireSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <rect width="48" height="48" fill="#2F2F2F" rx="8" />
    <path d="M12 40 Q24 8 36 40" stroke="#C0C0C0" strokeWidth="3" fill="none" />
    <path d="M16 36 Q24 16 32 36" stroke="#E5E5E5" strokeWidth="2" fill="none" />
    <circle cx="24" cy="24" r="8" stroke="#FFFFFF" strokeWidth="2" fill="none" />
    <path d="M8 24 L40 24" stroke="#A8A8A8" strokeWidth="1" />
    <path d="M24 8 L24 40" stroke="#A8A8A8" strokeWidth="1" />
    <circle cx="18" cy="18" r="2" fill="#FFD700" />
    <circle cx="30" cy="30" r="2" fill="#FFD700" />
    <rect x="22" y="40" width="4" height="4" fill="#696969" />
  </svg>
);

// Paper Sculpture - Origami-style paper art
export const PaperSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="paperGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFACD" />
        <stop offset="50%" stopColor="#F0E68C" />
        <stop offset="100%" stopColor="#DDA0DD" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#paperGrad)" rx="8" />
    <polygon points="24,8 36,20 24,32 12,20" fill="#FFFFFF" opacity="0.9" />
    <polygon points="24,16 30,22 24,28 18,22" fill="#F5F5F5" />
    <rect x="20" y="32" width="8" height="12" fill="#DDD" rx="2" />
    <path d="M12 20 L36 20" stroke="#E0E0E0" strokeWidth="1" />
    <path d="M24 8 L24 32" stroke="#E0E0E0" strokeWidth="1" />
    <circle cx="20" cy="16" r="1" fill="#FF69B4" />
    <circle cx="28" cy="24" r="1" fill="#87CEEB" />
  </svg>
);

// Ice Sculpture - Crystalline ice art
export const IceSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="iceGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E0F6FF" />
        <stop offset="50%" stopColor="#B0E0E6" />
        <stop offset="100%" stopColor="#87CEEB" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#iceGrad)" rx="8" />
    <polygon points="24,6 34,16 28,38 20,38 14,16" fill="#F0F8FF" opacity="0.9" />
    <polygon points="24,12 30,18 26,32 22,32 18,18" fill="#FFFFFF" opacity="0.8" />
    <rect x="20" y="38" width="8" height="6" fill="#4682B4" opacity="0.3" />
    <path d="M16 14 L32 26" stroke="#00BFFF" strokeWidth="1" opacity="0.7" />
    <path d="M32 14 L16 26" stroke="#00BFFF" strokeWidth="1" opacity="0.7" />
    <circle cx="20" cy="16" r="1.5" fill="#FFFFFF" />
    <circle cx="28" cy="24" r="1" fill="#87CEFA" />
  </svg>
);

// Sand Sculpture - Beach sand art
export const SandSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="sandGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#F4A460" />
        <stop offset="50%" stopColor="#DEB887" />
        <stop offset="100%" stopColor="#D2691E" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#sandGrad)" rx="8" />
    <path d="M24 10 L34 18 L30 36 L18 36 L14 18 Z" fill="#F5DEB3" opacity="0.8" />
    <ellipse cx="24" cy="22" rx="7" ry="5" fill="#FFEBCD" />
    <rect x="18" y="36" width="12" height="8" fill="#CD853F" rx="2" />
    <circle cx="20" cy="16" r="1" fill="#DDD26A" opacity="0.7" />
    <circle cx="28" cy="26" r="1" fill="#DAA520" opacity="0.6" />
    <circle cx="24" cy="30" r="0.5" fill="#B8860B" opacity="0.5" />
    <path d="M16 20 Q24 18 32 22" stroke="#CD853F" strokeWidth="1" opacity="0.5" />
  </svg>
);

// Mixed Media Sculpture - Colorful mixed materials
export const MixedMediaSculptureIcon: React.FC<IconProps> = ({ size = 48, className = "rounded-lg" }) => (
  <svg width={size} height={size} viewBox="0 0 48 48" className={className}>
    <defs>
      <linearGradient id="mixedGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FF6347" />
        <stop offset="25%" stopColor="#FFD700" />
        <stop offset="50%" stopColor="#32CD32" />
        <stop offset="75%" stopColor="#1E90FF" />
        <stop offset="100%" stopColor="#9370DB" />
      </linearGradient>
    </defs>
    <rect width="48" height="48" fill="url(#mixedGrad)" rx="8" />
    <path d="M24 8 L32 16 L28 32 L20 32 L16 16 Z" fill="#FFE4E1" opacity="0.8" />
    <circle cx="20" cy="18" r="3" fill="#FF69B4" opacity="0.7" />
    <rect x="26" y="20" width="4" height="8" fill="#00CED1" opacity="0.8" />
    <polygon points="24,26 28,30 20,30" fill="#32CD32" opacity="0.7" />
    <rect x="18" y="32" width="12" height="8" fill="#2F4F4F" rx="2" />
    <circle cx="22" cy="14" r="1" fill="#FFD700" />
    <circle cx="26" cy="16" r="1" fill="#FF4500" />
    <circle cx="24" cy="28" r="1" fill="#9370DB" />
  </svg>
);

// Export all sculpture icons
export const getUniqueSculptureIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  switch (materialName) {
    case 'Bronze Sculpture':
      return <BronzeSculptureIcon size={size} className={className} />;
    case 'Marble Sculpture':
      return <MarbleSculptureIcon size={size} className={className} />;
    case 'Clay Sculpture':
      return <ClaySculptureIcon size={size} className={className} />;
    case 'Ceramic Sculpture':
      return <CeramicSculptureIcon size={size} className={className} />;
    case 'Wood Carving':
      return <WoodCarvingIcon size={size} className={className} />;
    case 'Stone Carving':
      return <StoneCarvingIcon size={size} className={className} />;
    case 'Metal Sculpture':
      return <MetalSculptureIcon size={size} className={className} />;
    case 'Glass Sculpture':
      return <GlassSculptureIcon size={size} className={className} />;
    case 'Resin Sculpture':
      return <ResinSculptureIcon size={size} className={className} />;
    case 'Plaster Sculpture':
      return <PlasterSculptureIcon size={size} className={className} />;
    case 'Wire Sculpture':
      return <WireSculptureIcon size={size} className={className} />;
    case 'Paper Sculpture':
      return <PaperSculptureIcon size={size} className={className} />;
    case 'Ice Sculpture':
      return <IceSculptureIcon size={size} className={className} />;
    case 'Sand Sculpture':
      return <SandSculptureIcon size={size} className={className} />;
    case 'Mixed Media Sculpture':
      return <MixedMediaSculptureIcon size={size} className={className} />;
    default:
      return <BronzeSculptureIcon size={size} className={className} />;
  }
};
