import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Marble - Flowing veins like liquid marble
export const MarbleIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="marbleBase" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FFFFFF"/>
        <stop offset="50%" stopColor="#F8F8FF"/>
        <stop offset="100%" stopColor="#F0F0F0"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#marbleBase)" rx="2"/>
    <path d="M0,8 Q6,4 12,8 Q18,12 24,8 Q20,16 12,12 Q4,8 0,16" 
          fill="none" stroke="#D3D3D3" strokeWidth="1.5" opacity="0.8"/>
    <path d="M0,4 Q8,2 16,6 Q24,10 24,2" 
          fill="none" stroke="#C0C0C0" strokeWidth="1" opacity="0.6"/>
    <path d="M0,20 Q8,16 16,20 Q24,24 24,18" 
          fill="none" stroke="#B0B0B0" strokeWidth="0.8" opacity="0.5"/>
    <path d="M4,0 Q8,8 12,4 Q16,0 20,8 Q24,16 20,24" 
          fill="none" stroke="#A8A8A8" strokeWidth="0.6" opacity="0.4"/>
    <ellipse cx="8" cy="6" rx="2" ry="0.8" fill="#E8E8E8" opacity="0.7"/>
    <ellipse cx="16" cy="18" rx="1.5" ry="0.6" fill="#E8E8E8" opacity="0.6"/>
  </svg>
);

// Granite - Speckled crystalline structure
export const GraniteIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#696969" rx="2"/>
    <circle cx="3" cy="3" r="0.8" fill="#2F2F2F"/>
    <circle cx="7" cy="2" r="0.5" fill="#FFFFFF"/>
    <circle cx="11" cy="4" r="0.6" fill="#8B4513"/>
    <circle cx="15" cy="3" r="0.4" fill="#2F2F2F"/>
    <circle cx="19" cy="5" r="0.7" fill="#FFFFFF"/>
    <circle cx="21" cy="2" r="0.3" fill="#8B4513"/>
    <circle cx="2" cy="8" r="0.6" fill="#8B4513"/>
    <circle cx="6" cy="7" r="0.8" fill="#2F2F2F"/>
    <circle cx="10" cy="9" r="0.4" fill="#FFFFFF"/>
    <circle cx="14" cy="8" r="0.5" fill="#8B4513"/>
    <circle cx="18" cy="10" r="0.7" fill="#2F2F2F"/>
    <circle cx="22" cy="9" r="0.3" fill="#FFFFFF"/>
    <circle cx="4" cy="13" r="0.5" fill="#FFFFFF"/>
    <circle cx="8" cy="14" r="0.6" fill="#8B4513"/>
    <circle cx="12" cy="12" r="0.8" fill="#2F2F2F"/>
    <circle cx="16" cy="15" r="0.4" fill="#FFFFFF"/>
    <circle cx="20" cy="14" r="0.6" fill="#8B4513"/>
    <circle cx="1" cy="18" r="0.7" fill="#2F2F2F"/>
    <circle cx="5" cy="19" r="0.4" fill="#FFFFFF"/>
    <circle cx="9" cy="17" r="0.5" fill="#8B4513"/>
    <circle cx="13" cy="20" r="0.6" fill="#2F2F2F"/>
    <circle cx="17" cy="18" r="0.3" fill="#FFFFFF"/>
    <circle cx="21" cy="19" r="0.5" fill="#8B4513"/>
    <circle cx="3" cy="22" r="0.4" fill="#FFFFFF"/>
    <circle cx="7" cy="23" r="0.6" fill="#2F2F2F"/>
    <circle cx="11" cy="21" r="0.3" fill="#8B4513"/>
    <circle cx="15" cy="23" r="0.5" fill="#FFFFFF"/>
    <circle cx="19" cy="22" r="0.4" fill="#2F2F2F"/>
  </svg>
);

// Limestone - Layered sedimentary strata
export const LimestoneIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5F5DC" rx="2"/>
    <rect x="0" y="0" width="24" height="3" fill="#FAEBD7"/>
    <rect x="0" y="3" width="24" height="2" fill="#F0E68C"/>
    <rect x="0" y="5" width="24" height="4" fill="#DDD26A"/>
    <rect x="0" y="9" width="24" height="2" fill="#FAEBD7"/>
    <rect x="0" y="11" width="24" height="3" fill="#F0E68C"/>
    <rect x="0" y="14" width="24" height="2" fill="#DDD26A"/>
    <rect x="0" y="16" width="24" height="3" fill="#FAEBD7"/>
    <rect x="0" y="19" width="24" height="2" fill="#F0E68C"/>
    <rect x="0" y="21" width="24" height="3" fill="#DDD26A"/>
    <path d="M0,3 Q6,2 12,3 Q18,4 24,3" stroke="#E6E6FA" strokeWidth="0.5"/>
    <path d="M0,9 Q6,8 12,9 Q18,10 24,9" stroke="#E6E6FA" strokeWidth="0.5"/>
    <path d="M0,16 Q6,15 12,16 Q18,17 24,16" stroke="#E6E6FA" strokeWidth="0.5"/>
    <circle cx="8" cy="7" r="0.5" fill="#D2B48C" opacity="0.6"/>
    <circle cx="16" cy="13" r="0.4" fill="#D2B48C" opacity="0.5"/>
    <circle cx="20" cy="20" r="0.3" fill="#D2B48C" opacity="0.4"/>
  </svg>
);

// Travertine - Porous honeycomb texture
export const TravertineIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#FAEBD7" rx="2"/>
    <ellipse cx="4" cy="3" rx="2" ry="1.5" fill="#F5DEB3" opacity="0.8"/>
    <ellipse cx="12" cy="2" rx="1.5" ry="1" fill="#F5DEB3" opacity="0.7"/>
    <ellipse cx="20" cy="4" rx="1.8" ry="1.2" fill="#F5DEB3" opacity="0.6"/>
    <ellipse cx="2" cy="8" rx="1.2" ry="2" fill="#F5DEB3" opacity="0.7"/>
    <ellipse cx="8" cy="7" rx="2.5" ry="1.8" fill="#F5DEB3" opacity="0.8"/>
    <ellipse cx="16" cy="9" rx="1.6" ry="1.3" fill="#F5DEB3" opacity="0.6"/>
    <ellipse cx="22" cy="10" rx="1" ry="1.5" fill="#F5DEB3" opacity="0.5"/>
    <ellipse cx="5" cy="13" rx="1.8" ry="1.4" fill="#F5DEB3" opacity="0.7"/>
    <ellipse cx="13" cy="14" rx="2.2" ry="1.6" fill="#F5DEB3" opacity="0.8"/>
    <ellipse cx="21" cy="15" rx="1.4" ry="1.1" fill="#F5DEB3" opacity="0.6"/>
    <ellipse cx="3" cy="18" rx="1.6" ry="1.8" fill="#F5DEB3" opacity="0.7"/>
    <ellipse cx="10" cy="19" rx="1.9" ry="1.3" fill="#F5DEB3" opacity="0.6"/>
    <ellipse cx="18" cy="20" rx="1.3" ry="1.7" fill="#F5DEB3" opacity="0.8"/>
    <ellipse cx="7" cy="22" rx="1.5" ry="1" fill="#F5DEB3" opacity="0.5"/>
    <ellipse cx="15" cy="23" rx="1.1" ry="0.8" fill="#F5DEB3" opacity="0.4"/>
  </svg>
);

// Slate - Flat angular shale pieces
export const SlateIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#2F4F4F" rx="2"/>
    <polygon points="0,0 12,0 8,8 0,6" fill="#36454F"/>
    <polygon points="12,0 24,0 24,10 16,8 8,8" fill="#708090"/>
    <polygon points="0,6 8,8 4,16 0,14" fill="#708090"/>
    <polygon points="8,8 16,8 24,10 24,18 12,16 4,16" fill="#36454F"/>
    <polygon points="0,14 4,16 0,24" fill="#36454F"/>
    <polygon points="4,16 12,16 8,24 0,24" fill="#708090"/>
    <polygon points="12,16 24,18 24,24 8,24" fill="#36454F"/>
    <line x1="0" y1="6" x2="8" y2="8" stroke="#1C1C1C" strokeWidth="0.5"/>
    <line x1="8" y1="8" x2="16" y2="8" stroke="#1C1C1C" strokeWidth="0.5"/>
    <line x1="4" y1="16" x2="12" y2="16" stroke="#1C1C1C" strokeWidth="0.5"/>
    <line x1="12" y1="0" x2="8" y2="8" stroke="#1C1C1C" strokeWidth="0.5"/>
    <line x1="24" y1="10" x2="16" y2="8" stroke="#1C1C1C" strokeWidth="0.5"/>
  </svg>
);

// Quartzite - Crystalline faceted structure
export const QuartziteIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="quartzGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E6E6FA"/>
        <stop offset="50%" stopColor="#DDA0DD"/>
        <stop offset="100%" stopColor="#D8BFD8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#quartzGrad)" rx="2"/>
    <polygon points="12,2 18,6 15,12 9,12 6,6" fill="#F0E68C" opacity="0.8" stroke="#9370DB" strokeWidth="0.5"/>
    <polygon points="6,6 9,12 3,18 0,12" fill="#DDA0DD" opacity="0.7" stroke="#9370DB" strokeWidth="0.5"/>
    <polygon points="18,6 24,12 21,18 15,12" fill="#E6E6FA" opacity="0.8" stroke="#9370DB" strokeWidth="0.5"/>
    <polygon points="9,12 15,12 21,18 15,22 9,22 3,18" fill="#D8BFD8" opacity="0.9" stroke="#9370DB" strokeWidth="0.5"/>
    <polygon points="12,2 15,0 21,3 18,6" fill="#F8F8FF" opacity="0.6" stroke="#9370DB" strokeWidth="0.3"/>
    <polygon points="6,6 3,3 9,0 12,2" fill="#F8F8FF" opacity="0.6" stroke="#9370DB" strokeWidth="0.3"/>
    <polygon points="21,18 24,21 18,24 15,22" fill="#BA55D3" opacity="0.5" stroke="#9370DB" strokeWidth="0.3"/>
    <polygon points="3,18 0,21 6,24 9,22" fill="#BA55D3" opacity="0.5" stroke="#9370DB" strokeWidth="0.3"/>
    <circle cx="12" cy="12" r="1" fill="#FFFFFF" opacity="0.9"/>
  </svg>
);

// Sandstone - Wind-carved desert patterns
export const SandstoneIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="sandGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#F4A460"/>
        <stop offset="50%" stopColor="#DEB887"/>
        <stop offset="100%" stopColor="#D2B48C"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#sandGrad)" rx="2"/>
    <path d="M0,6 Q4,4 8,6 Q12,8 16,6 Q20,4 24,6 Q20,10 16,8 Q12,6 8,8 Q4,10 0,8" 
          fill="#CD853F" opacity="0.6"/>
    <path d="M0,14 Q4,12 8,14 Q12,16 16,14 Q20,12 24,14 Q20,18 16,16 Q12,14 8,16 Q4,18 0,16" 
          fill="#DDD26A" opacity="0.5"/>
    <path d="M0,22 Q4,20 8,22 Q12,24 16,22 Q20,20 24,22 L24,24 L0,24" 
          fill="#CD853F" opacity="0.7"/>
    <path d="M0,2 Q4,0 8,2 Q12,4 16,2 Q20,0 24,2 L24,6 Q20,4 16,6 Q12,8 8,6 Q4,4 0,6" 
          fill="#DDD26A" opacity="0.4"/>
    <circle cx="6" cy="10" r="0.5" fill="#8B7355" opacity="0.8"/>
    <circle cx="14" cy="18" r="0.4" fill="#8B7355" opacity="0.7"/>
    <circle cx="20" cy="8" r="0.3" fill="#8B7355" opacity="0.6"/>
  </svg>
);

// Onyx - Banded agate rings
export const OnyxIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#0F0F0F" rx="2"/>
    <ellipse cx="12" cy="12" rx="10" ry="8" fill="none" stroke="#FFFFFF" strokeWidth="1" opacity="0.8"/>
    <ellipse cx="12" cy="12" rx="8" ry="6" fill="none" stroke="#D3D3D3" strokeWidth="0.8" opacity="0.7"/>
    <ellipse cx="12" cy="12" rx="6" ry="4" fill="none" stroke="#FFFFFF" strokeWidth="0.6" opacity="0.6"/>
    <ellipse cx="12" cy="12" rx="4" ry="3" fill="none" stroke="#A8A8A8" strokeWidth="0.5" opacity="0.5"/>
    <ellipse cx="12" cy="12" rx="2" ry="1.5" fill="none" stroke="#FFFFFF" strokeWidth="0.4" opacity="0.4"/>
    <ellipse cx="12" cy="12" rx="1" ry="0.8" fill="#FFFFFF" opacity="0.3"/>
    <path d="M2,12 Q6,8 12,12 Q18,16 22,12" stroke="#696969" strokeWidth="0.3" fill="none" opacity="0.6"/>
    <path d="M4,8 Q8,6 12,8 Q16,10 20,8" stroke="#696969" strokeWidth="0.3" fill="none" opacity="0.5"/>
    <path d="M4,16 Q8,18 12,16 Q16,14 20,16" stroke="#696969" strokeWidth="0.3" fill="none" opacity="0.5"/>
  </svg>
);

// Basalt - Hexagonal columnar jointing
export const BasaltIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#36454F" rx="2"/>
    <polygon points="4,0 8,0 10,4 8,8 4,8 2,4" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="8,0 12,0 14,4 12,8 8,8 10,4" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="12,0 16,0 18,4 16,8 12,8 14,4" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="16,0 20,0 22,4 20,8 16,8 18,4" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="20,0 24,0 24,8 20,8 22,4" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="2,4 4,8 2,12 0,8" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="4,8 8,8 10,12 8,16 4,16 2,12" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="8,8 12,8 14,12 12,16 8,16 10,12" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="12,8 16,8 18,12 16,16 12,16 14,12" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="16,8 20,8 22,12 20,16 16,16 18,12" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="20,8 24,8 24,16 20,16 22,12" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="2,12 4,16 2,20 0,16" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="4,16 8,16 10,20 8,24 4,24 2,20" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="8,16 12,16 14,20 12,24 8,24 10,20" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="12,16 16,16 18,20 16,24 12,24 14,20" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="16,16 20,16 22,20 20,24 16,24 18,20" fill="#36454F" stroke="#1C1C1C" strokeWidth="0.5"/>
    <polygon points="20,16 24,16 24,24 20,24 22,20" fill="#2F4F4F" stroke="#1C1C1C" strokeWidth="0.5"/>
  </svg>
);

// River Rock - Smooth rounded pebbles
export const RiverRockIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#708090" rx="2"/>
    <ellipse cx="6" cy="4" rx="4" ry="3" fill="#A9A9A9"/>
    <ellipse cx="18" cy="6" rx="3" ry="4" fill="#696969"/>
    <ellipse cx="4" cy="12" rx="3" ry="2.5" fill="#808080"/>
    <ellipse cx="14" cy="10" rx="2.5" ry="3.5" fill="#A9A9A9"/>
    <ellipse cx="20" cy="14" rx="2" ry="3" fill="#696969"/>
    <ellipse cx="8" cy="18" rx="3.5" ry="2.8" fill="#808080"/>
    <ellipse cx="16" cy="20" rx="2.8" ry="2.2" fill="#A9A9A9"/>
    <ellipse cx="2" cy="20" rx="1.8" ry="2.5" fill="#696969"/>
    <ellipse cx="22" cy="22" rx="1.5" ry="1.8" fill="#808080"/>
    <ellipse cx="12" cy="16" rx="2.2" ry="1.5" fill="#A9A9A9"/>
    <ellipse cx="10" cy="2" rx="1.5" ry="1.2" fill="#696969"/>
    <ellipse cx="22" cy="2" rx="1.2" ry="1.8" fill="#808080"/>
  </svg>
);

// Fieldstone - Irregular natural stone wall
export const FieldstoneIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#8B7D6B" rx="2"/>
    <path d="M0,0 L8,0 Q10,2 8,6 L6,8 Q4,6 0,8 Z" fill="#A0522D" stroke="#654321" strokeWidth="0.5"/>
    <path d="M8,0 L16,0 Q18,3 16,6 L12,8 Q10,5 8,6 Q10,2 8,0" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.5"/>
    <path d="M16,0 L24,0 L24,8 Q20,6 18,8 Q16,6 16,3 Q18,3 16,0" fill="#A0522D" stroke="#654321" strokeWidth="0.5"/>
    <path d="M0,8 Q4,6 6,8 L8,12 Q6,14 4,12 L0,16 Z" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.5"/>
    <path d="M6,8 L12,8 Q14,10 12,14 L8,16 Q6,14 8,12 L6,8" fill="#A0522D" stroke="#654321" strokeWidth="0.5"/>
    <path d="M12,8 L18,8 Q20,6 24,8 L24,16 Q20,14 18,16 L12,14 Q14,10 12,8" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.5"/>
    <path d="M0,16 L4,12 Q6,14 8,16 L12,18 Q10,20 8,18 L4,20 Q2,18 0,20 Z" fill="#A0522D" stroke="#654321" strokeWidth="0.5"/>
    <path d="M8,16 L12,14 L18,16 Q20,14 24,16 L24,24 L16,24 Q14,22 12,24 L8,22 Q10,20 8,18 Q10,20 12,18 L8,16" fill="#D2B48C" stroke="#8B7355" strokeWidth="0.5"/>
    <path d="M0,20 Q2,18 4,20 L8,22 Q6,24 4,24 L0,24 Z" fill="#A0522D" stroke="#654321" strokeWidth="0.5"/>
  </svg>
);

// Cobblestone - Rounded street cobbles
export const CobblestoneIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#696969" rx="2"/>
    <ellipse cx="3" cy="3" rx="2.5" ry="2" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="9" cy="2" rx="2" ry="2.5" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="15" cy="3" rx="2.2" ry="2.2" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="21" cy="2" rx="1.8" ry="2.3" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="2" cy="8" rx="1.8" ry="2.5" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="7" cy="7" rx="2.3" ry="2" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="13" cy="8" rx="2" ry="2.3" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="19" cy="7" rx="2.5" ry="2.1" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="3" cy="13" rx="2.2" ry="2.4" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="9" cy="12" rx="2.4" ry="2" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="15" cy="13" rx="2.1" ry="2.3" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="21" cy="12" rx="2" ry="2.2" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="2" cy="18" rx="2.3" ry="2.1" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="8" cy="17" rx="2.1" ry="2.4" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="14" cy="18" rx="2.4" ry="2.2" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="20" cy="17" rx="2.2" ry="2.5" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="4" cy="22" rx="2" ry="1.8" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="10" cy="22" rx="2.3" ry="1.9" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="16" cy="22" rx="1.9" ry="1.8" fill="#808080" stroke="#2F2F2F" strokeWidth="0.5"/>
    <ellipse cx="22" cy="22" rx="1.8" ry="1.9" fill="#A9A9A9" stroke="#2F2F2F" strokeWidth="0.5"/>
  </svg>
);

// Stone Icon Mapper
export const getUniqueStoneIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Marble': return <MarbleIcon {...iconProps} />;
    case 'Granite': return <GraniteIcon {...iconProps} />;
    case 'Limestone': return <LimestoneIcon {...iconProps} />;
    case 'Travertine': return <TravertineIcon {...iconProps} />;
    case 'Slate': return <SlateIcon {...iconProps} />;
    case 'Quartzite': return <QuartziteIcon {...iconProps} />;
    case 'Sandstone': return <SandstoneIcon {...iconProps} />;
    case 'Onyx': return <OnyxIcon {...iconProps} />;
    case 'Basalt': return <BasaltIcon {...iconProps} />;
    case 'River Rock': return <RiverRockIcon {...iconProps} />;
    case 'Fieldstone': return <FieldstoneIcon {...iconProps} />;
    case 'Cobblestone': return <CobblestoneIcon {...iconProps} />;
    default: return <MarbleIcon {...iconProps} />;
  }
};
