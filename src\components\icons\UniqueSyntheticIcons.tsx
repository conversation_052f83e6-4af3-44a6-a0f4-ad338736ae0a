import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Acrylic - Clear plastic with sharp edges
export const AcrylicIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <linearGradient id="acrylicGrad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E6F3FF" stopOpacity="0.9"/>
        <stop offset="50%" stopColor="#FFFFFF" stopOpacity="0.7"/>
        <stop offset="100%" stopColor="#B3D9FF" stopOpacity="0.8"/>
      </linearGradient>
    </defs>
    <rect width="24" height="24" fill="url(#acrylicGrad)" rx="1" stroke="#4169E1" strokeWidth="0.8"/>
    <polygon points="2,2 12,2 10,12 2,10" fill="#FFFFFF" opacity="0.9"/>
    <polygon points="22,2 22,12 12,10 14,2" fill="#000000" opacity="0.1"/>
    <polygon points="2,22 10,12 12,22" fill="#000000" opacity="0.15"/>
    <polygon points="22,22 14,12 22,12" fill="#FFFFFF" opacity="0.8"/>
    <rect x="6" y="6" width="12" height="12" fill="none" stroke="#1E90FF" strokeWidth="0.5" opacity="0.6"/>
    <circle cx="12" cy="12" r="2" fill="#FFFFFF" opacity="0.95"/>
    <circle cx="6" cy="6" r="1" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="18" cy="18" r="1" fill="#FFFFFF" opacity="0.7"/>
  </svg>
);

// Plastic - Molded polymer with seam lines
export const PlasticIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#FF6B6B" rx="2"/>
    <path d="M0,12 L24,12" stroke="#FF4757" strokeWidth="1"/>
    <path d="M12,0 L12,24" stroke="#FF4757" strokeWidth="1"/>
    <circle cx="6" cy="6" r="3" fill="#FF5722" opacity="0.8"/>
    <circle cx="18" cy="6" r="2.5" fill="#FF5722" opacity="0.7"/>
    <circle cx="6" cy="18" r="2.8" fill="#FF5722" opacity="0.9"/>
    <circle cx="18" cy="18" r="2.2" fill="#FF5722" opacity="0.6"/>
    <rect x="10" y="10" width="4" height="4" fill="#FF3838" opacity="0.8"/>
    <polygon points="3,3 9,3 6,9" fill="#FFFFFF" opacity="0.3"/>
    <polygon points="15,3 21,3 18,9" fill="#FFFFFF" opacity="0.3"/>
    <polygon points="3,21 9,21 6,15" fill="#FFFFFF" opacity="0.3"/>
    <polygon points="15,21 21,21 18,15" fill="#FFFFFF" opacity="0.3"/>
    <circle cx="3" cy="12" r="0.5" fill="#2F2F2F"/>
    <circle cx="21" cy="12" r="0.5" fill="#2F2F2F"/>
    <circle cx="12" cy="3" r="0.5" fill="#2F2F2F"/>
    <circle cx="12" cy="21" r="0.5" fill="#2F2F2F"/>
  </svg>
);

// Vinyl - Flexible sheet material with texture
export const VinylIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#2F3542" rx="2"/>
    <circle cx="12" cy="12" r="10" fill="none" stroke="#57606F" strokeWidth="1"/>
    <circle cx="12" cy="12" r="8" fill="none" stroke="#57606F" strokeWidth="0.8"/>
    <circle cx="12" cy="12" r="6" fill="none" stroke="#57606F" strokeWidth="0.6"/>
    <circle cx="12" cy="12" r="4" fill="none" stroke="#57606F" strokeWidth="0.4"/>
    <circle cx="12" cy="12" r="2" fill="none" stroke="#57606F" strokeWidth="0.3"/>
    <circle cx="12" cy="12" r="1" fill="#A4B0BE"/>
    <rect x="11.5" y="2" width="1" height="4" fill="#A4B0BE"/>
    <rect x="11.5" y="18" width="1" height="4" fill="#A4B0BE"/>
    <rect x="2" y="11.5" width="4" height="1" fill="#A4B0BE"/>
    <rect x="18" y="11.5" width="4" height="1" fill="#A4B0BE"/>
    <path d="M12,4 Q16,8 12,12 Q8,8 12,4" fill="#747D8C" opacity="0.6"/>
    <path d="M12,12 Q16,16 12,20 Q8,16 12,12" fill="#747D8C" opacity="0.6"/>
    <path d="M4,12 Q8,8 12,12 Q8,16 4,12" fill="#747D8C" opacity="0.6"/>
    <path d="M12,12 Q16,8 20,12 Q16,16 12,12" fill="#747D8C" opacity="0.6"/>
  </svg>
);

// Laminate - Layered composite with wood grain print
export const LaminateIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DEB887" rx="2"/>
    <rect x="0" y="0" width="24" height="6" fill="#F4A460" opacity="0.8"/>
    <rect x="0" y="6" width="24" height="6" fill="#D2B48C" opacity="0.9"/>
    <rect x="0" y="12" width="24" height="6" fill="#F4A460" opacity="0.8"/>
    <rect x="0" y="18" width="24" height="6" fill="#D2B48C" opacity="0.9"/>
    <path d="M0,2 Q6,1 12,2 Q18,3 24,2" stroke="#CD853F" strokeWidth="0.5" opacity="0.7"/>
    <path d="M0,8 Q6,7 12,8 Q18,9 24,8" stroke="#CD853F" strokeWidth="0.5" opacity="0.7"/>
    <path d="M0,14 Q6,13 12,14 Q18,15 24,14" stroke="#CD853F" strokeWidth="0.5" opacity="0.7"/>
    <path d="M0,20 Q6,19 12,20 Q18,21 24,20" stroke="#CD853F" strokeWidth="0.5" opacity="0.7"/>
    <rect x="0" y="5.5" width="24" height="1" fill="#8B7355" opacity="0.8"/>
    <rect x="0" y="11.5" width="24" height="1" fill="#8B7355" opacity="0.8"/>
    <rect x="0" y="17.5" width="24" height="1" fill="#8B7355" opacity="0.8"/>
    <circle cx="6" cy="3" r="0.3" fill="#8B4513" opacity="0.6"/>
    <circle cx="18" cy="9" r="0.4" fill="#8B4513" opacity="0.7"/>
    <circle cx="12" cy="15" r="0.3" fill="#8B4513" opacity="0.5"/>
    <circle cx="20" cy="21" r="0.4" fill="#8B4513" opacity="0.8"/>
  </svg>
);

// Fiberglass - Woven glass fiber pattern
export const FiberglassIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F0F8FF" rx="2"/>
    <path d="M0,0 L6,6 M6,0 L12,6 M12,0 L18,6 M18,0 L24,6 M0,6 L6,12 M6,6 L12,12 M12,6 L18,12 M18,6 L24,12 M0,12 L6,18 M6,12 L12,18 M12,12 L18,18 M18,12 L24,18 M0,18 L6,24 M6,18 L12,24 M12,18 L18,24 M18,18 L24,24" 
          stroke="#E6F3FF" strokeWidth="1" opacity="0.6"/>
    <path d="M0,6 L6,0 M6,12 L12,6 M12,18 L18,12 M18,24 L24,18 M0,12 L6,6 M6,18 L12,12 M12,24 L18,18 M0,18 L6,12 M6,24 L12,18 M0,24 L6,18" 
          stroke="#B3D9FF" strokeWidth="1" opacity="0.6"/>
    <circle cx="3" cy="3" r="0.8" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="9" cy="9" r="0.6" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="15" cy="15" r="0.7" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="21" cy="21" r="0.5" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="21" cy="3" r="0.6" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="15" cy="9" r="0.8" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="9" cy="15" r="0.5" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="3" cy="21" r="0.7" fill="#FFFFFF" opacity="0.8"/>
  </svg>
);

// Carbon Fiber - Woven carbon pattern
export const CarbonFiberIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#2F2F2F" rx="2"/>
    <rect x="0" y="0" width="3" height="3" fill="#1C1C1C"/>
    <rect x="3" y="0" width="3" height="3" fill="#404040"/>
    <rect x="6" y="0" width="3" height="3" fill="#1C1C1C"/>
    <rect x="9" y="0" width="3" height="3" fill="#404040"/>
    <rect x="12" y="0" width="3" height="3" fill="#1C1C1C"/>
    <rect x="15" y="0" width="3" height="3" fill="#404040"/>
    <rect x="18" y="0" width="3" height="3" fill="#1C1C1C"/>
    <rect x="21" y="0" width="3" height="3" fill="#404040"/>
    <rect x="0" y="3" width="3" height="3" fill="#404040"/>
    <rect x="3" y="3" width="3" height="3" fill="#1C1C1C"/>
    <rect x="6" y="3" width="3" height="3" fill="#404040"/>
    <rect x="9" y="3" width="3" height="3" fill="#1C1C1C"/>
    <rect x="12" y="3" width="3" height="3" fill="#404040"/>
    <rect x="15" y="3" width="3" height="3" fill="#1C1C1C"/>
    <rect x="18" y="3" width="3" height="3" fill="#404040"/>
    <rect x="21" y="3" width="3" height="3" fill="#1C1C1C"/>
    <rect x="0" y="6" width="3" height="3" fill="#1C1C1C"/>
    <rect x="3" y="6" width="3" height="3" fill="#404040"/>
    <rect x="6" y="6" width="3" height="3" fill="#1C1C1C"/>
    <rect x="9" y="6" width="3" height="3" fill="#404040"/>
    <rect x="12" y="6" width="3" height="3" fill="#1C1C1C"/>
    <rect x="15" y="6" width="3" height="3" fill="#404040"/>
    <rect x="18" y="6" width="3" height="3" fill="#1C1C1C"/>
    <rect x="21" y="6" width="3" height="3" fill="#404040"/>
    <rect x="0" y="9" width="3" height="3" fill="#404040"/>
    <rect x="3" y="9" width="3" height="3" fill="#1C1C1C"/>
    <rect x="6" y="9" width="3" height="3" fill="#404040"/>
    <rect x="9" y="9" width="3" height="3" fill="#1C1C1C"/>
    <rect x="12" y="9" width="3" height="3" fill="#404040"/>
    <rect x="15" y="9" width="3" height="3" fill="#1C1C1C"/>
    <rect x="18" y="9" width="3" height="3" fill="#404040"/>
    <rect x="21" y="9" width="3" height="3" fill="#1C1C1C"/>
    <rect x="0" y="12" width="3" height="3" fill="#1C1C1C"/>
    <rect x="3" y="12" width="3" height="3" fill="#404040"/>
    <rect x="6" y="12" width="3" height="3" fill="#1C1C1C"/>
    <rect x="9" y="12" width="3" height="3" fill="#404040"/>
    <rect x="12" y="12" width="3" height="3" fill="#1C1C1C"/>
    <rect x="15" y="12" width="3" height="3" fill="#404040"/>
    <rect x="18" y="12" width="3" height="3" fill="#1C1C1C"/>
    <rect x="21" y="12" width="3" height="3" fill="#404040"/>
    <rect x="0" y="15" width="3" height="3" fill="#404040"/>
    <rect x="3" y="15" width="3" height="3" fill="#1C1C1C"/>
    <rect x="6" y="15" width="3" height="3" fill="#404040"/>
    <rect x="9" y="15" width="3" height="3" fill="#1C1C1C"/>
    <rect x="12" y="15" width="3" height="3" fill="#404040"/>
    <rect x="15" y="15" width="3" height="3" fill="#1C1C1C"/>
    <rect x="18" y="15" width="3" height="3" fill="#404040"/>
    <rect x="21" y="15" width="3" height="3" fill="#1C1C1C"/>
    <rect x="0" y="18" width="3" height="3" fill="#1C1C1C"/>
    <rect x="3" y="18" width="3" height="3" fill="#404040"/>
    <rect x="6" y="18" width="3" height="3" fill="#1C1C1C"/>
    <rect x="9" y="18" width="3" height="3" fill="#404040"/>
    <rect x="12" y="18" width="3" height="3" fill="#1C1C1C"/>
    <rect x="15" y="18" width="3" height="3" fill="#404040"/>
    <rect x="18" y="18" width="3" height="3" fill="#1C1C1C"/>
    <rect x="21" y="18" width="3" height="3" fill="#404040"/>
    <rect x="0" y="21" width="3" height="3" fill="#404040"/>
    <rect x="3" y="21" width="3" height="3" fill="#1C1C1C"/>
    <rect x="6" y="21" width="3" height="3" fill="#404040"/>
    <rect x="9" y="21" width="3" height="3" fill="#1C1C1C"/>
    <rect x="12" y="21" width="3" height="3" fill="#404040"/>
    <rect x="15" y="21" width="3" height="3" fill="#1C1C1C"/>
    <rect x="18" y="21" width="3" height="3" fill="#404040"/>
    <rect x="21" y="21" width="3" height="3" fill="#1C1C1C"/>
    <path d="M0,0 L24,24 M0,3 L21,24 M3,0 L24,21 M0,6 L18,24 M6,0 L24,18" stroke="#696969" strokeWidth="0.2" opacity="0.5"/>
    <path d="M24,0 L0,24 M21,0 L0,21 M24,3 L3,24 M18,0 L0,18 M24,6 L6,24" stroke="#696969" strokeWidth="0.2" opacity="0.5"/>
  </svg>
);

// Resin - Clear liquid polymer with bubbles
export const ResinIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <defs>
      <radialGradient id="resinGrad" cx="50%" cy="30%" r="70%">
        <stop offset="0%" stopColor="#FFFACD"/>
        <stop offset="50%" stopColor="#F0E68C"/>
        <stop offset="100%" stopColor="#DDD26A"/>
      </radialGradient>
    </defs>
    <rect width="24" height="24" fill="url(#resinGrad)" rx="2"/>
    <circle cx="6" cy="4" r="1.5" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="18" cy="6" r="1" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="4" cy="12" r="0.8" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="16" cy="14" r="1.2" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="8" cy="20" r="0.6" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="20" cy="18" r="0.9" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="12" cy="8" r="0.4" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="22" cy="10" r="0.5" fill="#FFFFFF" opacity="0.7"/>
    <circle cx="2" cy="18" r="0.7" fill="#FFFFFF" opacity="0.9"/>
    <circle cx="14" cy="22" r="0.3" fill="#FFFFFF" opacity="0.6"/>
    <ellipse cx="10" cy="16" rx="2" ry="1.5" fill="#FFFFFF" opacity="0.4"/>
    <ellipse cx="20" cy="4" rx="1.5" ry="1" fill="#FFFFFF" opacity="0.5"/>
    <path d="M4,8 Q8,6 12,8 Q16,10 20,8" stroke="#F4A460" strokeWidth="0.5" opacity="0.6"/>
    <path d="M2,16 Q6,14 10,16 Q14,18 18,16 Q22,14 24,16" stroke="#F4A460" strokeWidth="0.5" opacity="0.6"/>
  </svg>
);

// Polyurethane - Foam cellular structure
export const PolyurethaneIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#FFFACD" rx="2"/>
    <polygon points="3,2 7,2 5,6" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="7,2 11,2 9,6 5,6" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="11,2 15,2 13,6 9,6" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="15,2 19,2 17,6 13,6" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="19,2 23,2 21,6 17,6" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="1,6 5,6 3,10" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="5,6 9,6 7,10 3,10" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="9,6 13,6 11,10 7,10" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="13,6 17,6 15,10 11,10" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="17,6 21,6 19,10 15,10" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="21,6 24,6 24,10 19,10" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="3,10 7,10 5,14" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="7,10 11,10 9,14 5,14" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="11,10 15,10 13,14 9,14" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="15,10 19,10 17,14 13,14" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="19,10 24,10 24,14 17,14" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="1,14 5,14 3,18" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="5,14 9,14 7,18 3,18" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="9,14 13,14 11,18 7,18" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="13,14 17,14 15,18 11,18" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="17,14 24,14 24,18 15,18" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="3,18 7,18 5,22" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="7,18 11,18 9,22 5,22" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="11,18 15,18 13,22 9,22" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="15,18 24,18 24,22 13,22" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="5,22 9,22 7,24" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="9,22 13,22 11,24 7,24" fill="#F0E68C" stroke="#DDD26A" strokeWidth="0.5"/>
    <polygon points="13,22 24,22 24,24 11,24" fill="#F5DEB3" stroke="#DDD26A" strokeWidth="0.5"/>
  </svg>
);

// Synthetic Icon Mapper
export const getUniqueSyntheticIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Acrylic': return <AcrylicIcon {...iconProps} />;
    case 'Plastic': return <PlasticIcon {...iconProps} />;
    case 'Vinyl': return <VinylIcon {...iconProps} />;
    case 'Laminate': return <LaminateIcon {...iconProps} />;
    case 'Fiberglass': return <FiberglassIcon {...iconProps} />;
    case 'Carbon Fiber': return <CarbonFiberIcon {...iconProps} />;
    case 'Resin': return <ResinIcon {...iconProps} />;
    case 'Polyurethane': return <PolyurethaneIcon {...iconProps} />;
    default: return <AcrylicIcon {...iconProps} />;
  }
};
