import React from 'react';

interface IconProps {
  size?: number;
  className?: string;
}

// Oak Wood - Tree rings pattern (cross-section view)
export const OakWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DEB887" rx="2"/>
    <circle cx="12" cy="12" r="10" fill="none" stroke="#8B4513" strokeWidth="0.8"/>
    <circle cx="12" cy="12" r="7" fill="none" stroke="#8B4513" strokeWidth="0.6"/>
    <circle cx="12" cy="12" r="4" fill="none" stroke="#8B4513" strokeWidth="0.5"/>
    <circle cx="12" cy="12" r="2" fill="none" stroke="#8B4513" strokeWidth="0.4"/>
    <circle cx="12" cy="12" r="0.8" fill="#654321"/>
    <path d="M12,2 L12,22 M2,12 L22,12" stroke="#654321" strokeWidth="0.3" opacity="0.6"/>
  </svg>
);

// Walnut Wood - Flowing river pattern
export const WalnutWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#8B4513" rx="2"/>
    <path d="M0,4 Q6,8 12,4 Q18,0 24,4 L24,8 Q18,4 12,8 Q6,12 0,8 Z" fill="#654321"/>
    <path d="M0,12 Q6,16 12,12 Q18,8 24,12 L24,16 Q18,12 12,16 Q6,20 0,16 Z" fill="#3C1810"/>
    <path d="M0,20 Q6,24 12,20 Q18,16 24,20 L24,24 L0,24 Z" fill="#654321"/>
    <circle cx="8" cy="6" r="1.5" fill="#2F1B14" opacity="0.8"/>
    <circle cx="16" cy="18" r="1" fill="#2F1B14" opacity="0.6"/>
  </svg>
);

// Cherry Wood - Diagonal plank boards
export const CherryWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#D2691E" rx="2"/>
    <polygon points="0,0 8,0 12,24 0,24" fill="#CD853F"/>
    <polygon points="8,0 16,0 20,24 12,24" fill="#A0522D"/>
    <polygon points="16,0 24,0 24,24 20,24" fill="#CD853F"/>
    <line x1="8" y1="0" x2="12" y2="24" stroke="#8B4513" strokeWidth="0.5"/>
    <line x1="16" y1="0" x2="20" y2="24" stroke="#8B4513" strokeWidth="0.5"/>
    <circle cx="4" cy="8" r="0.5" fill="#8B4513"/>
    <circle cx="14" cy="16" r="0.3" fill="#8B4513"/>
  </svg>
);

// Maple Wood - Hexagonal honeycomb pattern
export const MapleWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F4A460" rx="2"/>
    <polygon points="6,2 10,4 10,8 6,10 2,8 2,4" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
    <polygon points="14,2 18,4 18,8 14,10 10,8 10,4" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
    <polygon points="22,2 24,4 24,8 22,10 18,8 18,4" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
    <polygon points="6,10 10,12 10,16 6,18 2,16 2,12" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
    <polygon points="14,10 18,12 18,16 14,18 10,16 10,12" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
    <polygon points="22,10 24,12 24,16 22,18 18,16 18,12" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
    <polygon points="6,18 10,20 10,24 6,24 2,24 2,20" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
    <polygon points="14,18 18,20 18,24 14,24 10,24 10,20" fill="none" stroke="#DEB887" strokeWidth="0.8"/>
  </svg>
);

// Birch Wood - Birch bark with black horizontal stripes
export const BirchWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#F5F5DC" rx="2"/>
    <rect x="0" y="3" width="24" height="2" fill="#2F2F2F"/>
    <rect x="0" y="7" width="18" height="1" fill="#2F2F2F"/>
    <rect x="0" y="11" width="24" height="2.5" fill="#2F2F2F"/>
    <rect x="0" y="16" width="15" height="1.5" fill="#2F2F2F"/>
    <rect x="0" y="20" width="24" height="1" fill="#2F2F2F"/>
    <ellipse cx="6" cy="4" rx="1.5" ry="0.8" fill="#2F2F2F"/>
    <ellipse cx="20" cy="8" rx="1" ry="0.5" fill="#2F2F2F"/>
    <ellipse cx="12" cy="12.5" rx="2" ry="1" fill="#2F2F2F"/>
    <ellipse cx="8" cy="17" rx="1.2" ry="0.6" fill="#2F2F2F"/>
  </svg>
);

// Pine Wood - Christmas tree silhouette pattern
export const PineWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DDD26A" rx="2"/>
    <polygon points="4,12 8,4 12,12" fill="#228B22" opacity="0.6"/>
    <polygon points="12,12 16,4 20,12" fill="#228B22" opacity="0.6"/>
    <polygon points="2,20 6,12 10,20" fill="#228B22" opacity="0.4"/>
    <polygon points="14,20 18,12 22,20" fill="#228B22" opacity="0.4"/>
    <rect x="7" y="20" width="2" height="4" fill="#8B4513"/>
    <rect x="15" y="20" width="2" height="4" fill="#8B4513"/>
    <circle cx="8" cy="8" r="2" fill="#654321"/>
    <circle cx="16" cy="16" r="1.5" fill="#654321"/>
    <circle cx="8" cy="8" r="0.8" fill="#2F2F2F"/>
    <circle cx="16" cy="16" r="0.6" fill="#2F2F2F"/>
  </svg>
);

// Mahogany Wood - Interlocking puzzle pieces
export const MahoganyWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#C04000" rx="2"/>
    <path d="M0,0 L12,0 Q15,3 12,6 L12,12 Q9,15 12,18 L12,24 L0,24 Z" fill="#8B0000"/>
    <path d="M12,0 L24,0 L24,12 Q21,15 18,12 L12,12 Q9,9 12,6 Q15,3 12,0" fill="#654321"/>
    <path d="M12,12 L18,12 Q21,9 24,12 L24,24 L12,24 Q9,21 12,18 Q15,15 12,12" fill="#8B0000"/>
    <circle cx="6" cy="6" r="1" fill="#4A0000"/>
    <circle cx="18" cy="18" r="1" fill="#4A0000"/>
  </svg>
);

// Teak Wood - Oil droplet pattern
export const TeakWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#B8860B" rx="2"/>
    <ellipse cx="6" cy="4" rx="3" ry="2" fill="#DAA520" opacity="0.8"/>
    <ellipse cx="18" cy="8" rx="2.5" ry="1.5" fill="#DAA520" opacity="0.7"/>
    <ellipse cx="4" cy="12" rx="2" ry="3" fill="#DAA520" opacity="0.6"/>
    <ellipse cx="16" cy="16" rx="4" ry="2" fill="#DAA520" opacity="0.8"/>
    <ellipse cx="8" cy="20" rx="2.5" ry="1.8" fill="#DAA520" opacity="0.7"/>
    <ellipse cx="20" cy="20" rx="1.5" ry="2.5" fill="#DAA520" opacity="0.6"/>
    <circle cx="12" cy="6" r="0.8" fill="#FFD700"/>
    <circle cx="10" cy="14" r="0.6" fill="#FFD700"/>
    <circle cx="20" cy="12" r="0.5" fill="#FFD700"/>
  </svg>
);

// Bamboo - Vertical segments with nodes
export const BambooIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#DAA520" rx="2"/>
    <rect x="2" y="0" width="6" height="6" fill="#F4A460" stroke="#8B7355" strokeWidth="1"/>
    <rect x="8" y="0" width="6" height="6" fill="#DEB887" stroke="#8B7355" strokeWidth="1"/>
    <rect x="14" y="0" width="8" height="6" fill="#F4A460" stroke="#8B7355" strokeWidth="1"/>
    <rect x="2" y="6" width="6" height="6" fill="#DEB887" stroke="#8B7355" strokeWidth="1"/>
    <rect x="8" y="6" width="6" height="6" fill="#F4A460" stroke="#8B7355" strokeWidth="1"/>
    <rect x="14" y="6" width="8" height="6" fill="#DEB887" stroke="#8B7355" strokeWidth="1"/>
    <rect x="2" y="12" width="6" height="6" fill="#F4A460" stroke="#8B7355" strokeWidth="1"/>
    <rect x="8" y="12" width="6" height="6" fill="#DEB887" stroke="#8B7355" strokeWidth="1"/>
    <rect x="14" y="12" width="8" height="6" fill="#F4A460" stroke="#8B7355" strokeWidth="1"/>
    <rect x="2" y="18" width="6" height="6" fill="#DEB887" stroke="#8B7355" strokeWidth="1"/>
    <rect x="8" y="18" width="6" height="6" fill="#F4A460" stroke="#8B7355" strokeWidth="1"/>
    <rect x="14" y="18" width="8" height="6" fill="#DEB887" stroke="#8B7355" strokeWidth="1"/>
  </svg>
);

// Reclaimed Wood - Patchwork of different wood pieces
export const ReclaimedWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#8B7355" rx="2"/>
    <polygon points="0,0 10,0 8,12 0,12" fill="#A0522D"/>
    <polygon points="10,0 24,0 24,8 12,10 8,12 10,0" fill="#696969"/>
    <polygon points="0,12 8,12 12,10 24,8 24,16 16,18 0,20" fill="#654321"/>
    <polygon points="0,20 16,18 24,16 24,24 0,24" fill="#8B4513"/>
    <circle cx="4" cy="6" r="0.8" fill="#2F2F2F"/>
    <circle cx="18" cy="4" r="0.6" fill="#2F2F2F"/>
    <circle cx="12" cy="14" r="0.5" fill="#2F2F2F"/>
    <rect x="6" y="2" width="0.5" height="3" fill="#2F2F2F"/>
    <rect x="20" y="10" width="0.5" height="4" fill="#2F2F2F"/>
    <path d="M2,8 L6,4 M14,6 L18,2 M8,16 L12,20" stroke="#2F2F2F" strokeWidth="0.5"/>
  </svg>
);

// Driftwood - Smooth wave-like erosion pattern
export const DriftwoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#A0522D" rx="4"/>
    <path d="M0,8 Q6,4 12,8 Q18,12 24,8 L24,16 Q18,12 12,16 Q6,20 0,16 Z" fill="#D3D3D3"/>
    <path d="M0,0 Q6,4 12,0 Q18,4 24,0 L24,8 Q18,12 12,8 Q6,4 0,8 Z" fill="#808080"/>
    <path d="M0,16 Q6,20 12,16 Q18,20 24,16 L24,24 Q18,20 12,24 Q6,20 0,24 Z" fill="#696969"/>
    <ellipse cx="8" cy="4" rx="2" ry="1" fill="#A9A9A9" opacity="0.6"/>
    <ellipse cx="16" cy="12" rx="1.5" ry="0.8" fill="#A9A9A9" opacity="0.5"/>
    <ellipse cx="6" cy="20" rx="1.8" ry="1.2" fill="#A9A9A9" opacity="0.4"/>
  </svg>
);

// Ebony Wood - Fingerprint pattern
export const EbonyWoodIcon: React.FC<IconProps> = ({ size = 24, className = "" }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" className={className}>
    <rect width="24" height="24" fill="#0F0F0F" rx="2"/>
    <path d="M12,2 Q8,6 12,10 Q16,6 12,2" fill="none" stroke="#2F2F2F" strokeWidth="0.8"/>
    <path d="M12,4 Q10,6 12,8 Q14,6 12,4" fill="none" stroke="#2F2F2F" strokeWidth="0.6"/>
    <path d="M12,14 Q8,18 12,22 Q16,18 12,14" fill="none" stroke="#2F2F2F" strokeWidth="0.8"/>
    <path d="M12,16 Q10,18 12,20 Q14,18 12,16" fill="none" stroke="#2F2F2F" strokeWidth="0.6"/>
    <path d="M4,12 Q8,8 12,12 Q8,16 4,12" fill="none" stroke="#2F2F2F" strokeWidth="0.8"/>
    <path d="M20,12 Q16,8 12,12 Q16,16 20,12" fill="none" stroke="#2F2F2F" strokeWidth="0.8"/>
    <circle cx="12" cy="12" r="1" fill="#404040" opacity="0.5"/>
  </svg>
);

// Wood Icon Mapper
export const getUniqueWoodIcon = (materialName: string, size: number = 48, className: string = "rounded-lg") => {
  const iconProps = { size, className };
  
  switch (materialName) {
    case 'Oak Wood': return <OakWoodIcon {...iconProps} />;
    case 'Walnut Wood': return <WalnutWoodIcon {...iconProps} />;
    case 'Cherry Wood': return <CherryWoodIcon {...iconProps} />;
    case 'Maple Wood': return <MapleWoodIcon {...iconProps} />;
    case 'Birch Wood': return <BirchWoodIcon {...iconProps} />;
    case 'Pine Wood': return <PineWoodIcon {...iconProps} />;
    case 'Mahogany Wood': return <MahoganyWoodIcon {...iconProps} />;
    case 'Teak Wood': return <TeakWoodIcon {...iconProps} />;
    case 'Bamboo': return <BambooIcon {...iconProps} />;
    case 'Reclaimed Wood': return <ReclaimedWoodIcon {...iconProps} />;
    case 'Driftwood': return <DriftwoodIcon {...iconProps} />;
    case 'Ebony Wood': return <EbonyWoodIcon {...iconProps} />;
    default: return <OakWoodIcon {...iconProps} />;
  }
};
