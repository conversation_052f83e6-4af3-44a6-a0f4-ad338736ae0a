// License Management System for Altora

export type LicenseType = 'personal' | 'commercial' | 'enterprise';
export type SubscriptionTier = 'free' | 'personal' | 'pro' | 'team';

export interface LicenseInfo {
  type: LicenseType;
  tier: SubscriptionTier;
  features: string[];
  restrictions: string[];
  watermark: boolean;
  maxImages: number;
  maxUsers: number;
  commercialUse: boolean;
  resolutionLevel: 'standard' | 'high' | 'highest';
}

export const LICENSE_CONFIGS: Record<SubscriptionTier, LicenseInfo> = {
  free: {
    type: 'personal',
    tier: 'free',
    features: [
      '3 design generations per month',
      'Access to 5 popular design styles',
      'Standard resolution outputs',
      'Basic material library'
    ],
    restrictions: [
      'Personal use only',
      'Watermarked images',
      'Limited style selection',
      'No commercial use'
    ],
    watermark: true,
    maxImages: 3,
    maxUsers: 1,
    commercialUse: false,
    resolutionLevel: 'standard'
  },
  personal: {
    type: 'personal',
    tier: 'personal',
    features: [
      '250 images per month',
      'Access to all design styles',
      'Normal resolution',
      'Full material library',
      'Email support'
    ],
    restrictions: [
      'Personal use only',
      'Small watermark',
      'No commercial licensing'
    ],
    watermark: true,
    maxImages: 250,
    maxUsers: 1,
    commercialUse: false,
    resolutionLevel: 'high'
  },
  pro: {
    type: 'commercial',
    tier: 'pro',
    features: [
      '1,000 images per month',
      'Commercial license included',
      'No watermark',
      'Highest resolution',
      'Priority support',
      'Custom style training (coming soon)'
    ],
    restrictions: [
      'Single user license'
    ],
    watermark: false,
    maxImages: 1000,
    maxUsers: 1,
    commercialUse: true,
    resolutionLevel: 'highest'
  },
  team: {
    type: 'enterprise',
    tier: 'team',
    features: [
      '5,000 images per month',
      'Commercial license included',
      'No watermark',
      'Highest resolution',
      'Up to 5 users',
      'Train your own style',
      'Priority support',
      'Team collaboration tools'
    ],
    restrictions: [
      'Team size limited to 5 users'
    ],
    watermark: false,
    maxImages: 5000,
    maxUsers: 5,
    commercialUse: true,
    resolutionLevel: 'highest'
  }
};

export class LicenseManager {
  static getLicenseInfo(tier: SubscriptionTier): LicenseInfo {
    return LICENSE_CONFIGS[tier];
  }

  static canUseCommercially(tier: SubscriptionTier): boolean {
    return LICENSE_CONFIGS[tier].commercialUse;
  }

  static hasWatermark(tier: SubscriptionTier): boolean {
    return LICENSE_CONFIGS[tier].watermark;
  }

  static getMaxImages(tier: SubscriptionTier): number {
    return LICENSE_CONFIGS[tier].maxImages;
  }

  static getMaxUsers(tier: SubscriptionTier): number {
    return LICENSE_CONFIGS[tier].maxUsers;
  }

  static getResolutionLevel(tier: SubscriptionTier): string {
    return LICENSE_CONFIGS[tier].resolutionLevel;
  }

  static generateLicenseText(tier: SubscriptionTier, imageId?: string): string {
    const license = LICENSE_CONFIGS[tier];
    const timestamp = new Date().toISOString().split('T')[0];
    
    if (license.commercialUse) {
      return `COMMERCIAL LICENSE
Generated by Altora AI Design Studio
License Type: ${license.type.toUpperCase()}
Subscription: ${tier.toUpperCase()}
Generated: ${timestamp}
${imageId ? `Image ID: ${imageId}` : ''}

This image is licensed for commercial use including:
- Marketing materials and advertisements
- Client presentations and proposals
- Website and social media content
- Print and digital publications
- Resale as part of design services

License holder retains full commercial rights to this AI-generated design.
For questions about licensing, contact <EMAIL>`;
    } else {
      return `PERSONAL USE LICENSE
Generated by Altora AI Design Studio
License Type: PERSONAL USE ONLY
Subscription: ${tier.toUpperCase()}
Generated: ${timestamp}
${imageId ? `Image ID: ${imageId}` : ''}

This image is licensed for personal use only including:
- Personal home design inspiration
- Non-commercial social media sharing
- Personal portfolio (non-commercial)

Commercial use is NOT permitted including:
- Client work or paid services
- Marketing or advertising
- Resale or redistribution
- Business presentations

Upgrade to Pro or Team plan for commercial licensing.
Contact <EMAIL> for licensing questions.`;
    }
  }

  static validateUsage(tier: SubscriptionTier, usageType: 'personal' | 'commercial'): {
    allowed: boolean;
    message: string;
  } {
    const license = LICENSE_CONFIGS[tier];
    
    if (usageType === 'commercial' && !license.commercialUse) {
      return {
        allowed: false,
        message: `Commercial use requires Pro or Team subscription. Your current ${tier} plan only allows personal use.`
      };
    }
    
    return {
      allowed: true,
      message: `Usage approved for ${tier} subscription.`
    };
  }
}

// Usage tracking interface
export interface UsageStats {
  userId: string;
  tier: SubscriptionTier;
  imagesGenerated: number;
  imagesRemaining: number;
  billingPeriodStart: Date;
  billingPeriodEnd: Date;
  lastGenerated?: Date;
}

export class UsageTracker {
  static calculateRemaining(stats: UsageStats): number {
    const maxImages = LicenseManager.getMaxImages(stats.tier);
    return Math.max(0, maxImages - stats.imagesGenerated);
  }

  static canGenerate(stats: UsageStats): boolean {
    return this.calculateRemaining(stats) > 0;
  }

  static getUsagePercentage(stats: UsageStats): number {
    const maxImages = LicenseManager.getMaxImages(stats.tier);
    return Math.min(100, (stats.imagesGenerated / maxImages) * 100);
  }
}
